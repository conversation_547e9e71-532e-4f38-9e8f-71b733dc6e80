#!/bin/bash

# Step-by-step installation script for Python 3.8 + Apple Silicon M4
# This script installs packages one by one to avoid dependency conflicts

echo "Installing packages step by step for Python 3.8 + Apple Silicon..."

# Step 1: Install PyTorch first (most important)
echo "Step 1: Installing PyTorch..."
pip install torch==1.13.1 torchvision==0.14.1 torchaudio==0.13.1

# Step 2: Install torch-scatter
echo "Step 2: Installing torch-scatter..."
pip install torch-scatter==2.0.9

# Step 3: Install core ML packages
echo "Step 3: Installing transformers and datasets..."
pip install transformers==4.22.1
pip install datasets==2.6.1

# Step 4: Install pytorch-lightning (try different versions if needed)
echo "Step 4: Installing pytorch-lightning..."
pip install pytorch-lightning==1.7.7 || pip install pytorch-lightning==1.8.0 || pip install pytorch-lightning==1.8.1

# Step 5: Install configuration utilities
echo "Step 5: Installing configuration utilities..."
pip install jsonnet==0.18.0
pip install easydict==1.9
pip install tqdm==4.64.1

# Step 6: Install experiment tracking
echo "Step 6: Installing wandb..."
pip install wandb==0.13.4

# Step 7: Install other utilities
echo "Step 7: Installing other utilities..."
pip install tfrecord==1.14.1
pip install frozendict==2.3.4
pip install bitarray==2.6.0
pip install ujson==5.5.0
pip install gitpython==3.1.29
pip install ninja==********
pip install absl-py==1.3.0
pip install tensorboard==2.10.1

# Step 8: Install scientific computing packages
echo "Step 8: Installing scientific packages..."
pip install scipy==1.9.3
pip install scikit-learn==1.1.3

# Step 9: Install spacy (might need special handling)
echo "Step 9: Installing spacy..."
pip install spacy==3.4.4

# Step 10: Install setuptools
echo "Step 10: Installing setuptools..."
pip install setuptools==56.1.0

echo "Installation completed! Now install FAISS and ColBERT manually:"
echo "1. conda install -c conda-forge faiss-cpu"
echo "2. cd src/ColBERT && pip install -e . && cd ../.."
