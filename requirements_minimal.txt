# Minimal requirements for Python 3.8 + Apple Silicon M4
# Install these after PyTorch and torch-scatter

# Core ML packages (versions that don't require Rust compilation)
transformers==4.21.3
datasets==2.5.2

# Training framework (flexible version)
pytorch-lightning>=1.7.0,<2.0.0

# Configuration
jsonnet
easydict
tqdm

# Experiment tracking
wandb>=0.13.0,<0.14.0

# Utilities
tfrecord
frozendict
bitarray
ujson
gitpython
ninja
absl-py
tensorboard

# Scientific computing
scipy
scikit-learn

# Build tools
setuptools>=56.0.0
