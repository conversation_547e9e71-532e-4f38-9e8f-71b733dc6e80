# Robust Table QA Environment Requirements
# Optimized for Apple Silicon (M4 chip) Mac Air
# Based on the environment setup from README.md
# Updated for Python 3.9+ compatibility

# Core PyTorch packages (compatible with Python 3.8 and Apple Silicon)
# Note: PyTorch with MPS support for Apple Silicon, Python 3.8 compatible versions
torch==1.13.1
torchvision==0.14.1
torchaudio==0.13.1

# PyTorch extensions (Python 3.8 compatible)
torch-scatter==2.0.9

# Transformers and datasets (Python 3.8 compatible versions)
transformers==4.22.1
datasets==2.6.1

# Configuration and utilities (Python 3.8 compatible versions)
jsonnet==0.18.0
easydict==1.9
tqdm==4.64.1
pytorch-lightning==1.7.7
tfrecord==1.14.1
frozendict==2.3.4

# Experiment tracking (Python 3.8 compatible)
wandb==0.13.4

# FAISS for similarity search (CPU version for Apple Silicon)
# Note: Install with conda: conda install -c conda-forge faiss-cpu -y
# For pip users, use: faiss-cpu

# Additional utilities (Python 3.8 compatible versions)
bitarray==2.6.0
spacy==3.4.4
ujson==5.5.0
gitpython==3.1.29
ninja==********
absl-py==1.3.0
tensorboard==2.10.1

# Scientific computing (Python 3.8 compatible versions)
scipy==1.9.3
scikit-learn==1.1.3

# Build tools
setuptools==56.1.0

# ColBERT (local installation)
# Note: Install with: pip install -e src/ColBERT
