# Robust Table QA Environment Requirements
# Optimized for Apple Silicon (M4 chip) Mac Air
# Based on the environment setup from README.md
# Updated for Python 3.9+ compatibility

# Core PyTorch packages (CPU/MPS version for Apple Silicon)
# Note: PyTorch with MPS (Metal Performance Shaders) support for Apple Silicon
torch>=1.12.0
torchvision>=0.13.0
torchaudio>=0.12.0

# PyTorch extensions (CPU version for Apple Silicon)
torch-scatter

# Transformers and datasets
transformers==4.22.1
datasets==2.6.1

# Configuration and utilities
jsonnet
easydict
tqdm
pytorch-lightning==1.8.2
tfrecord
frozendict

# Experiment tracking
wandb==0.13.4

# FAISS for similarity search (CPU version for Apple Silicon)
# Note: Install with conda: conda install -c conda-forge faiss-cpu -y
# For pip users, use: faiss-cpu

# Additional utilities
bitarray
spacy
ujson
gitpython
ninja
absl-py
tensorboard

# Scientific computing
scipy
scikit-learn

# Build tools
setuptools==56.1.0

# ColBERT (local installation)
# Note: Install with: pip install -e src/ColBERT
