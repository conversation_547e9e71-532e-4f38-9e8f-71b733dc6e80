# Robust Table QA Environment Requirements
# Based on the environment setup from README.md

# Core PyTorch packages (CUDA 11.1 version)
# Note: Install these with specific CUDA version using:
# pip install torch==1.10.1+cu111 torchvision==0.11.2+cu111 torchaudio==0.10.1 -f https://download.pytorch.org/whl/torch_stable.html
torch==1.10.1
torchvision==0.11.2
torchaudio==0.10.1

# PyTorch extensions
# Note: Install with: pip install torch-scatter -f https://data.pyg.org/whl/torch-1.10.1+cu111.html --force-reinstall --no-cache-dir --no-index
torch-scatter

# Transformers and datasets
transformers==4.22.1
datasets==2.6.1

# Configuration and utilities
jsonnet
easydict
tqdm
pytorch-lightning==1.8.2
tfrecord
frozendict

# Experiment tracking
wandb==0.13.4

# FAISS for similarity search
# Note: Install with conda: conda install -c pytorch faiss-gpu -y
# For pip users, use: faiss-gpu

# Additional utilities
bitarray
spacy
ujson
gitpython
ninja
absl-py
tensorboard

# Scientific computing
scipy
scikit-learn

# Build tools
setuptools==56.1.0

# ColBERT (local installation)
# Note: Install with: pip install -e src/ColBERT
