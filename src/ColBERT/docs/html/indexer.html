
<!DOCTYPE html>

<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Indexer &#8212; ColBERT v2 documentation</title>
    <link rel="stylesheet" href="docs/_static/pygments.css" type="text/css" />
    <link rel="stylesheet" href="docs/_static/alabaster.css" type="text/css" />
    <script id="documentation_options" data-url_root="./" src="docs/_static/documentation_options.js"></script>
    <script src="docs/_static/jquery.js"></script>
    <script src="docs/_static/underscore.js"></script>
    <script src="docs/_static/doctools.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Searcher" href="searcher.html" />
    <link rel="prev" title="Trainer" href="trainer.html" />
   
  <link rel="stylesheet" href="docs/_static/custom.css" type="text/css" />
  
  
  <meta name="viewport" content="width=device-width, initial-scale=0.9, maximum-scale=0.9" />

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <div class="section" id="indexer">
<h1>Indexer<a class="headerlink" href="#indexer" title="Permalink to this headline">¶</a></h1>
<div class="section" id="functions">
<h2>Functions<a class="headerlink" href="#functions" title="Permalink to this headline">¶</a></h2>
<dl class="py function">
<dt id="indexer.Indexer">
<code class="sig-prename descclassname"><span class="pre">indexer.</span></code><code class="sig-name descname"><span class="pre">Indexer</span></code><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">checkpoint</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">config</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indexer.Indexer" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt id="indexer.Indexer.__init__">
<code class="sig-prename descclassname"><span class="pre">indexer.Indexer.</span></code><code class="sig-name descname"><span class="pre">__init__</span></code><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">checkpoint</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">config</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indexer.Indexer.__init__" title="Permalink to this definition">¶</a></dt>
<dd><p>Use Run().context() to choose the run’s configuration. They are NOT extracted from <cite>config</cite>.</p>
</dd></dl>

<dl class="py function">
<dt id="indexer.Indexer.configure">
<code class="sig-prename descclassname"><span class="pre">indexer.Indexer.</span></code><code class="sig-name descname"><span class="pre">configure</span></code><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw_args</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indexer.Indexer.configure" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt id="indexer.Indexer.get_index">
<code class="sig-prename descclassname"><span class="pre">indexer.Indexer.</span></code><code class="sig-name descname"><span class="pre">get_index</span></code><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indexer.Indexer.get_index" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt id="indexer.Indexer.erase">
<code class="sig-prename descclassname"><span class="pre">indexer.Indexer.</span></code><code class="sig-name descname"><span class="pre">erase</span></code><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indexer.Indexer.erase" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt id="indexer.Indexer.index">
<code class="sig-prename descclassname"><span class="pre">indexer.Indexer.</span></code><code class="sig-name descname"><span class="pre">index</span></code><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">collection</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indexer.Indexer.index" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt id="indexer.Indexer.__launch">
<code class="sig-prename descclassname"><span class="pre">indexer.Indexer.</span></code><code class="sig-name descname"><span class="pre">__launch</span></code><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">collection</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indexer.Indexer.__launch" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</div>
</div>


          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="index.html">ColBERT</a></h1>








<h3>Navigation</h3>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="trainer.html">Trainer</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Indexer</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#functions">Functions</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="searcher.html">Searcher</a></li>
</ul>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="index.html">Documentation overview</a><ul>
      <li>Previous: <a href="trainer.html" title="previous chapter">Trainer</a></li>
      <li>Next: <a href="searcher.html" title="next chapter">Searcher</a></li>
  </ul></li>
</ul>
</div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" />
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>$('#searchbox').show(0);</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &copy;2022, Omar Khattab, Keshav Santhanam, Jon Saad-Falcon, Christopher Potts, Matei Zaharia.
      
      |
      Powered by <a href="http://sphinx-doc.org/">Sphinx 3.5.1</a>
      &amp; <a href="https://github.com/bitprophet/alabaster">Alabaster 0.7.12</a>
      
      |
      <a href="_sources/indexer.rst.txt"
          rel="nofollow">Page source</a>
    </div>

    

    
  </body>
</html>