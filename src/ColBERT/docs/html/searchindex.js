Search.setIndex({docnames:["index","indexer","searcher","trainer"],envversion:{"sphinx.domains.c":2,"sphinx.domains.changeset":1,"sphinx.domains.citation":1,"sphinx.domains.cpp":3,"sphinx.domains.index":1,"sphinx.domains.javascript":2,"sphinx.domains.math":2,"sphinx.domains.python":2,"sphinx.domains.rst":2,"sphinx.domains.std":2,sphinx:56},filenames:["index.rst","indexer.rst","searcher.rst","trainer.rst"],objects:{"indexer.Indexer":{__init__:[1,0,1,""],__launch:[1,0,1,""],configure:[1,0,1,""],erase:[1,0,1,""],get_index:[1,0,1,""],index:[1,0,1,""]},"searcher.Searcher":{__init__:[2,0,1,""],_search_all_Q:[2,0,1,""],configure:[2,0,1,""],dense_search:[2,0,1,""],encode:[2,0,1,""],search:[2,0,1,""]},"trainer.Trainer":{__init__:[3,0,1,""],best_checkpoint_path:[3,0,1,""],configure:[3,0,1,""],train:[3,0,1,""]},indexer:{Indexer:[1,0,1,""]},searcher:{Searcher:[2,0,1,""]},trainer:{Trainer:[3,0,1,""]}},objnames:{"0":["py","function","Python function"]},objtypes:{"0":"py:function"},terms:{"function":0,"int":2,NOT:1,Use:1,__init__:[1,2,3],__launch:1,_search_all_q:2,accur:[2,3],base:3,bert:3,best_checkpoint_path:3,checkpoint:[1,2,3],choos:1,colbert:2,collect:[1,2,3],config:[1,2,3],configur:[1,2,3],context:1,data:2,dense_search:2,dict:2,encod:2,eras:1,extract:1,fals:1,from:1,get_index:1,help:[2,3],here:3,ignor:3,index:[0,2],initi:[2,3],kw_arg:[1,2,3],list:2,name:1,none:[1,2,3],note:3,onli:3,overwrit:1,queri:[2,3],run:1,search:2,searcher:0,see:[2,3],self:[1,2,3],signatur:[2,3],str:2,suppli:3,tensor:2,text:2,thei:1,torch:2,train:3,trainer:0,tripl:3,type:[2,3],uncas:3,union:2,used:3},titles:["Welcome to ColBERT\u2019s documentation!","Indexer","Searcher","Trainer"],titleterms:{"function":[1,2,3],api:0,colbert:0,compon:0,document:0,index:1,searcher:2,trainer:3,welcom:0}})