
<!DOCTYPE html>

<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Trainer &#8212; ColBERT v2 documentation</title>
    <link rel="stylesheet" href="docs/_static/pygments.css" type="text/css" />
    <link rel="stylesheet" href="docs/_static/alabaster.css" type="text/css" />
    <script id="documentation_options" data-url_root="./" src="docs/_static/documentation_options.js"></script>
    <script src="docs/_static/jquery.js"></script>
    <script src="docs/_static/underscore.js"></script>
    <script src="docs/_static/doctools.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Indexer" href="indexer.html" />
    <link rel="prev" title="Welcome to ColBERT’s documentation!" href="index.html" />
   
  <link rel="stylesheet" href="docs/_static/custom.css" type="text/css" />
  
  
  <meta name="viewport" content="width=device-width, initial-scale=0.9, maximum-scale=0.9" />

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <div class="section" id="trainer">
<h1>Trainer<a class="headerlink" href="#trainer" title="Permalink to this headline">¶</a></h1>
<div class="section" id="functions">
<h2>Functions<a class="headerlink" href="#functions" title="Permalink to this headline">¶</a></h2>
<dl class="py function">
<dt id="trainer.Trainer">
<code class="sig-prename descclassname"><span class="pre">trainer.</span></code><code class="sig-name descname"><span class="pre">Trainer</span></code><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">triples</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">queries</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">collection</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">config</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#trainer.Trainer" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt id="trainer.Trainer.__init__">
<code class="sig-prename descclassname"><span class="pre">trainer.Trainer.</span></code><code class="sig-name descname"><span class="pre">__init__</span></code><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">triples</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">queries</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">collection</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">config</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#trainer.Trainer.__init__" title="Permalink to this definition">¶</a></dt>
<dd><p>Initialize self.  See help(type(self)) for accurate signature.</p>
</dd></dl>

<dl class="py function">
<dt id="trainer.Trainer.configure">
<code class="sig-prename descclassname"><span class="pre">trainer.Trainer.</span></code><code class="sig-name descname"><span class="pre">configure</span></code><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw_args</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#trainer.Trainer.configure" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt id="trainer.Trainer.train">
<code class="sig-prename descclassname"><span class="pre">trainer.Trainer.</span></code><code class="sig-name descname"><span class="pre">train</span></code><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">checkpoint</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'bert-base-uncased'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#trainer.Trainer.train" title="Permalink to this definition">¶</a></dt>
<dd><p>Note that config.checkpoint is ignored. Only the supplied checkpoint here is used.</p>
</dd></dl>

<dl class="py function">
<dt id="trainer.Trainer.best_checkpoint_path">
<code class="sig-prename descclassname"><span class="pre">trainer.Trainer.</span></code><code class="sig-name descname"><span class="pre">best_checkpoint_path</span></code><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#trainer.Trainer.best_checkpoint_path" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</div>
</div>


          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="index.html">ColBERT</a></h1>








<h3>Navigation</h3>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Trainer</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#functions">Functions</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="indexer.html">Indexer</a></li>
<li class="toctree-l1"><a class="reference internal" href="searcher.html">Searcher</a></li>
</ul>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="index.html">Documentation overview</a><ul>
      <li>Previous: <a href="index.html" title="previous chapter">Welcome to ColBERT’s documentation!</a></li>
      <li>Next: <a href="indexer.html" title="next chapter">Indexer</a></li>
  </ul></li>
</ul>
</div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" />
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>$('#searchbox').show(0);</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &copy;2022, Omar Khattab, Keshav Santhanam, Jon Saad-Falcon, Christopher Potts, Matei Zaharia.
      
      |
      Powered by <a href="http://sphinx-doc.org/">Sphinx 3.5.1</a>
      &amp; <a href="https://github.com/bitprophet/alabaster">Alabaster 0.7.12</a>
      
      |
      <a href="_sources/trainer.rst.txt"
          rel="nofollow">Page source</a>
    </div>

    

    
  </body>
</html>