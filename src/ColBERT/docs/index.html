
<!DOCTYPE html>

<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Welcome to ColBERT’s documentation! &#8212; ColBERT v2 documentation</title>
    <script id="documentation_options" src="./documentation_options.js"></script>
    <script src="./jquery.js"></script>
    <script src="./underscore.js"></script>
    <script src="./doctools.js"></script>
    <link rel="index" title="Index" href="docs/html/genindex.html" />
    <link rel="search" title="Search" href="docs/html/search.html" />
    <link rel="next" title="Trainer" href="docs/html/trainer.html" />
   
  
  
  <meta name="viewport" content="width=device-width, initial-scale=0.9, maximum-scale=0.9" />

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <div class="section" id="welcome-to-colbert-s-documentation">
<h1>Welcome to ColBERT’s documentation!<a class="headerlink" href="#welcome-to-colbert-s-documentation" title="Permalink to this headline">¶</a></h1>
<div class="section" id="api-components">
<h2>API Components<a class="headerlink" href="#api-components" title="Permalink to this headline">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="html/trainer.html">Trainer</a><ul>
<li class="toctree-l2"><a class="reference internal" href="html/trainer.html#functions">Functions</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="html/indexer.html">Indexer</a><ul>
<li class="toctree-l2"><a class="reference internal" href="html/indexer.html#functions">Functions</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="html/searcher.html">Searcher</a><ul>
<li class="toctree-l2"><a class="reference internal" href="html/searcher.html#functions">Functions</a></li>
</ul>
</li>
</ul>
</div>
</div>
</div>


          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="#">ColBERT</a></h1>








<h3>Navigation</h3>
<ul>
<li class="toctree-l1"><a class="reference internal" href="html/trainer.html">Trainer</a></li>
<li class="toctree-l1"><a class="reference internal" href="html/indexer.html">Indexer</a></li>
<li class="toctree-l1"><a class="reference internal" href="html/searcher.html">Searcher</a></li>
</ul>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="#">Documentation overview</a><ul>
      <li>Next: <a href="html/trainer.html" title="next chapter">Trainer</a></li>
  </ul></li>
</ul>
</div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="html/search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" />
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>$('#searchbox').show(0);</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &copy;2022, Omar Khattab, Keshav Santhanam, Jon Saad-Falcon, Christopher Potts, Matei Zaharia.
      
      |
      Powered by <a href="http://sphinx-doc.org/">Sphinx 3.5.1</a>
      &amp; <a href="https://github.com/bitprophet/alabaster">Alabaster 0.7.12</a>
      
      |
      <a href="_sources/index.rst.txt"
          rel="nofollow">Page source</a>
    </div>

    

    
  </body>
</html>