<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;6c69f93d-3379-44fd-b71b-1f3cfcb9b039&quot;,&quot;conversations&quot;:{&quot;6c69f93d-3379-44fd-b71b-1f3cfcb9b039&quot;:{&quot;id&quot;:&quot;6c69f93d-3379-44fd-b71b-1f3cfcb9b039&quot;,&quot;createdAtIso&quot;:&quot;2025-08-28T15:38:55.867Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-08-28T15:53:10.484Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;bdca28a4-6a1c-4468-9858-6736f7a6daf6&quot;,&quot;request_message&quot;:&quot; 帮忙总结下这个项目&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot; 帮忙总结下这个项目&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_01P5U4bVJy5rjLdZ4L4uCXJk&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;.\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;我来帮您总结这个项目。首先让我查看项目的整体结构和主要文件。\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot; 帮忙总结下这个项目&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/Documents/Code/Experiment/robust-tableqa-main&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/Documents/Code/Experiment/robust-tableqa-main&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-28T15:52:53.762Z&quot;,&quot;response_text&quot;:&quot;我来帮您总结这个项目。首先让我查看项目的整体结构和主要文件。\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;68164c93-ae3a-4583-8af3-1571f746c08d&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_01NF2T3egqvfu1RXNTW3RKvJ&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;README.md\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;让我查看README文件来了解项目的基本信息：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in ., excluding hidden items:\n./CONTRIBUTING.md\n./LICENSE\n./LICENSE-SAMPLECODE\n./LICENSE-SUMMARY\n./README.md\n./configs\n./src\n./configs/base_env.jsonnet\n./configs/e2e_wtq\n./configs/nq_tables\n./configs/wikisql\n./configs/wtq\n./src/ColBERT\n./src/data_loader_manager\n./src/main.py\n./src/models\n./src/trainers\n./src/utils\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_01P5U4bVJy5rjLdZ4L4uCXJk&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/Documents/Code/Experiment/robust-tableqa-main&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/Documents/Code/Experiment/robust-tableqa-main&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-28T15:52:57.277Z&quot;,&quot;response_text&quot;:&quot;让我查看README文件来了解项目的基本信息：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;88cc4cc6-efdf-48f1-8dab-45c9785dfaf5&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_01Hic3AC6RneFeMLPdJ2mFdC&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;src\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查看项目的源代码结构来更好地了解实现细节：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on README.md:\n     1\t## Robust Table Question Answering\n     2\t\n     3\tCode for training and evaluation the transformer-based robust Table QA models introduced in the following ACL 2023 papers:\n     4\t\n     5\t&lt;div align=\&quot;center\&quot;&gt;    \n     6\t \n     7\t### An &lt;u&gt;I&lt;/u&gt;nner &lt;u&gt;T&lt;/u&gt;able &lt;u&gt;R&lt;/u&gt;etriever for Robust Table Question Answering\n     8\t\n     9\t[![Paper](https://img.shields.io/badge/Paper-Amazon_Science-orange)](https://www.amazon.science/publications/li-rage-late-interaction-retrieval-augmented-generation-with-explicit-signals-for-open-domain-table-question-answering)\n    10\t[![Conference](https://img.shields.io/badge/Conference-ACL--2023-blue)](https://2023.aclweb.org/)\n    11\t[![License: CC BY-NC 4.0](https://img.shields.io/badge/License-CC%20BY--NC%204.0-lightgrey.svg)](https://creativecommons.org/licenses/by-nc/4.0/)\n    12\t\n    13\t&lt;/div&gt;\n    14\t\n    15\tInner Table Retriever (ITR) is a general-purpose approach for handling long tables in TableQA \n    16\tthat extracts sub-tables to preserve the most relevant information for a question. \n    17\tITR can be easily integrated into existing systems to improve their accuracy achieve state-of-the-art results.\n    18\t\n    19\tIf you find our paper, code or framework useful, please put a link to this repo and reference this work in your paper:\n    20\t\n    21\t```\n    22\t@Inproceedings{Lin2023,\n    23\t author = {Weizhe Lin and Rexhina Blloshmi  and Bill Byrne and Adrià de Gispert and Gonzalo Iglesias},\n    24\t title = {An inner table retriever for robust table question answering},\n    25\t year = {2023},\n    26\t url = {https://www.amazon.science/publications/an-inner-table-retriever-for-robust-table-question-answering},\n    27\t booktitle = {ACL 2023},\n    28\t}\n    29\t```\n    30\t\n    31\tFor more info and details on how to install/run check [Scripts for Inner Table Retriever](#scripts-for-inner-table-retriever) in the [Codebase](#codebase) Section below. \n    32\t\n    33\t&lt;hr&gt; \n    34\t\n    35\t&lt;div align=\&quot;center\&quot;&gt;\n    36\t \n    37\t### LI-RAGE: &lt;u&gt;L&lt;/u&gt;ate &lt;u&gt;I&lt;/u&gt;nteraction &lt;u&gt;R&lt;/u&gt;etrieval &lt;u&gt;A&lt;/u&gt;ugmented &lt;u&gt;G&lt;/u&gt;eneration with &lt;u&gt;E&lt;/u&gt;xplicit Signals for Open-Domain Table Question Answering\n    38\t\n    39\t[![Paper](https://img.shields.io/badge/Paper-Amazon_Science-orange)](https://www.amazon.science/publications/li-rage-late-interaction-retrieval-augmented-generation-with-explicit-signals-for-open-domain-table-question-answering)\n    40\t[![Conference](https://img.shields.io/badge/Conference-ACL_2023-red)](https://2023.aclweb.org/)\n    41\t[![License: CC BY-NC 4.0](https://img.shields.io/badge/License-CC%20BY--NC%204.0-lightgrey.svg)](https://creativecommons.org/licenses/by-nc/4.0/)\n    42\t\n    43\t&lt;/div&gt;\n    44\tLI-RAGE is a framework for open-domain TableQA which addresses several limitations thanks to: \n    45\t\n    46\t1) applying late interaction models which enforce a finer-grained interaction between question and table embeddings at retrieval time. \n    47\t2) incorporating a joint training scheme of the retriever and reader with explicit table-level signals, and \n    48\t3) embedding a binary relevance token as a prefix to the answer generated by the reader, \n    49\tso we can determine at inference time whether the table used to answer the question is reliable and filter accordingly. \n    50\tThe combined strategies set a new state-to-the-art performance on two public open-domain TableQA datasets.\n    51\t\n    52\t\n    53\t\n    54\tIf you find our paper, code or framework useful, please put a link to this repo and reference this work in your paper:\n    55\t\n    56\t```\n    57\t@Inproceedings{Lin2023,\n    58\t author = {Weizhe Lin and Rexhina Blloshmi and Bill Byrne and Adrià de Gispert and Gonzalo Iglesias},\n    59\t title = {LI-RAGE: Late interaction retrieval augmented generation with explicit signals for open-domain table question answering},\n    60\t year = {2023},\n    61\t url = {https://www.amazon.science/publications/li-rage-late-interaction-retrieval-augmented-generation-with-explicit-signals-for-open-domain-table-question-answering},\n    62\t booktitle = {ACL 2023},\n    63\t}\n    64\t```\n    65\t\n    66\tFor more info and details on how to install/run check [Scripts for Open-domain TableQA with Late Interaction Models](#scripts-for-open-domain-tableqa-with-late-interaction-models) in the [Codebase](#codebase) Section below.\n    67\t\n    68\t&lt;hr&gt;\n    69\t\n    70\t## Codebase \n    71\t\n    72\t- [Codebase Structure](#codebase-structure)\n    73\t  - [3rd-party Open-Source Tools](#3rd-party-open-source-tools)\n    74\t  - [Overview](#overview)\n    75\t    - [Structure](#structure)\n    76\t    - [Configs](#configs)\n    77\t    - [ModuleParser](#moduleparser)\n    78\t    - [MetricsProcessor](#metricsprocessor)\n    79\t- [Environment](#environment)\n    80\t- [Data](#data)\n    81\t- [Useful Command-line Arguments](#useful-command-line-arguments)\n    82\t  - [Universal](#universal)\n    83\t  - [Training](#training)\n    84\t  - [Testing](#testing)\n    85\t- [Scripts for Inner Table Retriever](#scripts-for-inner-table-retriever)\n    86\t  - [Inner Table Retrieval](#inner-table-retrieval)\n    87\t    - [Main Experiments](#main-experiments)\n    88\t    - [Additional ablation experiments](#additional-ablation-experiments)\n    89\t  - [TableQA with ITR (TaPEx + ITR)](#tableqa-with-itr-tapex--itr)\n    90\t    - [Main Experiments](#main-experiments-1)\n    91\t    - [Additional ablation experiments](#additional-ablation-experiments-1)\n    92\t  - [TableQA with ITR (TaPas + ITR)](#tableqa-with-itr-tapas--itr)\n    93\t  - [TableQA with ITR (OmniTab + ITR)](#tableqa-with-itr-omnitab--itr)\n    94\t  - [Disable ITR](#disable-itr)\n    95\t  - [TableQA Baselines](#tableqa-baselines)\n    96\t- [Scripts for Open-domain TableQA with Late Interaction Models](#scripts-for-open-domain-tableqa-with-late-interaction-models)\n    97\t  - [Main Experiments](#main-experiments-2)\n    98\t  - [Additional ablation experiments](#additional-ablation-experiments-2)\n    99\t\n   100\tThe code base is created by Weizhe Lin (<EMAIL>), during his internship as an applied scientist of Alexa AI. \n   101\tThis code base contains:\n   102\t- TableQA Baseline Systems\n   103\t    - TAPAS (Huggingface)\n   104\t    - TAPEX (Huggingface)\n   105\t    - OmniTab (Huggingface)\n   106\t- Inner Table Retrieval (ITR)\n   107\t    - Inner Table Retriever based on Dense Passage Retrieval (DPR)\n   108\t    - TableQA systems with ITR\n   109\t-  Late Interaction Models for Retrieval Augmented TableQA Systems\n   110\t    - Late Interaction Table Retriever based on ColBERT\n   111\t    - Retrieval Augmented TableQA Models (TaPEx) with Late Interaction Retriever\n   112\t    - Dense Table Retriever (DPR)\n   113\t    - Retrieval Augmented TableQA Models (TaPEx) with DPR\n   114\t\n   115\t\n   116\t# Codebase Structure\n   117\t\n   118\t## 3rd-party Open-Source Tools\n   119\tIn this codebase, several open-source tools are used, including:\n   120\t- Weights and Biases\n   121\t- Pytorch-lightning\n   122\t- Pytorch\n   123\t- Pytorch-scatter\n   124\t- Huggingface-transformers\n   125\t- Other open-source tools\n   126\t  - Experiment framework is built upon [RAVQA](https://github.com/LinWeizheDragon/Retrieval-Augmented-Visual-Question-Answering)\n   127\t\n   128\t\n   129\t## Overview\n   130\tThe training and testing are backboned by pytorch-lightning. The pre-trained Transformer models are from Huggingface-transformers. The training platform is Pytorch.\n   131\t\n   132\t### Structure\n   133\tThe framework consists of:\n   134\t\n   135\t1. **main.py**: the entry point to the main program. It loads a config file and override some entries with command-line arguments. It initialises a data loader wrapper, a model trainer, and a pytorch-lightning trainer to execute training and testing.\n   136\t2. **Data Loader Wrapper**: it loads the data according to `data_loader.dataset_modules` defined in config files. A data module called `LoadDataLoaders` is used to create pytorch dataloaders from the data.\n   137\t3. **Datasets**: they are automatically loaded by the data loader wrapper. `.collate_fn` is defined to collate the data. An decorator class `ModuleParser` is used to help generate the training inputs. This decorator class generates input dict according to configs (`config.model_config.input_modules/decorder_input_modules/output_modules`).\n   138\t4. **Model Trainers**: a pytorch-lightning `LightningModule` instance. It defines training/testing behaviors (training steps, optimizers, schedulers, logging, checkpointing, and so on). It initialises the model being trained at `self.model`.\n   139\t5. **Models**: pytorch `nn.Modules` models.\n   140\t\n   141\t### Configs\n   142\tThe configuration is achieved with `jsonnet`. It enables inheritance of config files. For example, `wtq/tapex_ITR_mix_wtq.jsonnet` override its configs to `wtq/tapex_ITR_column_wise_wtq.jsonnet`, which again inherits from `base_env.jsonnet` where most of common configurations are defined.\n   143\t\n   144\tBy including the corresponding key:value pair in the config file, overriding can be easily performed.\n   145\t\n   146\t### ModuleParser\n   147\tA decorator class that helps to parse data into features that are used by models.\n   148\t\n   149\tAn example taken from ITR is shown below:\n   150\t```\n   151\t\&quot;input_modules\&quot;: {\n   152\t    \&quot;module_list\&quot;:[\n   153\t    {\&quot;type\&quot;: \&quot;QuestionInput\&quot;,  \&quot;option\&quot;: \&quot;default\&quot;, \n   154\t                \&quot;separation_tokens\&quot;: {\&quot;start\&quot;: \&quot;\&quot;, \&quot;end\&quot;: \&quot;\&quot;}},\n   155\t    ],\n   156\t    \&quot;postprocess_module_list\&quot;: [\n   157\t    {\&quot;type\&quot;: \&quot;PostProcessInputTokenization\&quot;, \&quot;option\&quot;: \&quot;default\&quot;},\n   158\t    ],\n   159\t},\n   160\t\&quot;decoder_input_modules\&quot;: {\n   161\t    \&quot;module_list\&quot;:[\n   162\t    {\&quot;type\&quot;: \&quot;TextBasedTableInput\&quot;,  \&quot;option\&quot;: \&quot;default\&quot;,\n   163\t                \&quot;separation_tokens\&quot;: {\&quot;header_start\&quot;: \&quot;&lt;HEADER&gt;\&quot;, \&quot;header_sep\&quot;: \&quot;&lt;HEADER_SEP&gt;\&quot;, \&quot;header_end\&quot;: \&quot;&lt;HEADER_END&gt;\&quot;, \&quot;row_start\&quot;: \&quot;&lt;ROW&gt;\&quot;, \&quot;row_sep\&quot;: \&quot;&lt;ROW_SEP&gt;\&quot;, \&quot;row_end\&quot;: \&quot;&lt;ROW_END&gt;\&quot;}},\n   164\t    ],\n   165\t    \&quot;postprocess_module_list\&quot;: [\n   166\t    {\&quot;type\&quot;: \&quot;PostProcessDecoderInputTokenization\&quot;, \&quot;option\&quot;: \&quot;default\&quot;},\n   167\t    ],\n   168\t},\n   169\t\&quot;output_modules\&quot;: {\n   170\t    \&quot;module_list\&quot;:[\n   171\t    {\&quot;type\&quot;: \&quot;SimilarityOutput\&quot;, \&quot;option\&quot;: \&quot;default\&quot;},\n   172\t    ],\n   173\t    \&quot;postprocess_module_list\&quot;: [\n   174\t    {\&quot;type\&quot;: \&quot;PostProcessConcatenateLabels\&quot;, \&quot;option\&quot;: \&quot;default\&quot;},\n   175\t    ],\n   176\t},\n   177\t```\n   178\twhich first run input modules in the order defined in `input_modules`, and then the postprocessing unit `PostProcessInputTokenization` is used to tokenize the input into `input_ids` and `input_attention_mask`.\n   179\t\n   180\tSimilarly, `decoder_input_modules` (in the ITR, decoder input is used as the input to the item encoder. The naming is slightly confusing but feel free to change it, though not deemed necessary) generates `item_input_ids` and `item_input_attention_mask`. `output_modules` generates the labels for ITR training.\n   181\t\n   182\tBy defining new functions in `ModuleParser`, e.g. `self.TextBasedVisionInput`, a new behavior can be easily introduced to transform modules into training features.\n   183\t\n   184\t### MetricsProcessor\n   185\tThe following entries in config file `test.metrics` define the metrics to compute in validation and testing. Each module uploads `log_dict` with `metrics_name: metrics_value` which can be processed in trainers conveniently.\n   186\t```\n   187\t\&quot;metrics\&quot;: [\n   188\t    {'name': 'compute_exact_match'},\n   189\t    {'name': 'compute_retrieval_metrics'},\n   190\t],\n   191\t```\n   192\t\n   193\t\n   194\t# Environment\n   195\t```\n   196\tconda create -n tableqa python=3.8\n   197\tconda activate tableqa\n   198\tpip install torch==1.10.1+cu111 torchvision==0.11.2+cu111 torchaudio==0.10.1 -f https://download.pytorch.org/whl/torch_stable.html\n   199\tpip install torch-scatter -f https://data.pyg.org/whl/torch-1.10.1+cu111.html --force-reinstall --no-cache-dir --no-index\n   200\tpip install transformers==4.22.1 datasets==2.6.1\n   201\tpip install jsonnet\n   202\tpip install easydict tqdm pytorch-lightning==1.8.2 tfrecord frozendict\n   203\tpip install wandb==0.13.4\n   204\tconda install -c pytorch faiss-gpu -y\n   205\tpip install bitarray spacy ujson gitpython\n   206\tpip install ninja\n   207\tpip install absl-py tensorboard\n   208\tpip install -e src/ColBERT\n   209\tpip install scipy scikit-learn\n   210\tpip install setuptools==56.1.0\n   211\t```\n   212\t\n   213\tNote: All experiments were run on 8 V100/A100 clusters. You may want to reduce batch sizes or use smaller base models (e.g. TaPEx-base) to fit the memory if your GPUs are smaller.\n   214\t\n   215\t# Data\n   216\tWe use open-sourced data which can be found in the respective papers, please check the references in our papers. Make sure to add/change the data paths in the respective config files. \n   217\t\n   218\t# Useful Command-line Arguments\n   219\tSome general cli arguments. For more details, please read the code / directly look at how they are used in training/evaluation of specific models.\n   220\t\n   221\t## Universal\n   222\t- All trainer parameters supported by pytorch-lightning, such as `--accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2`\n   223\t- `--experiment_name EXPERIMENT_NAME` the name of the experiment. Will be used as the name of the folder as well as the run name on WANDB\n   224\t- `--mode [train/test]` indicate the mode for running. \n   225\t- `--modules module1 module2 module3 ...` list of modules that will be used. They will be saved to `self.config.model_config.modules` so that they are accessible anywhere in the framework.\n   226\t\n   227\t\n   228\t## Training\n   229\t\n   230\t- `--opts [list of configurations]` used at the end of the cli command. `self.config` will be overwritten by the configurations here. For example:\n   231\t\n   232\t  - `train.batch_size=1` batch size\n   233\t  - `train.scheduler=linear` currently supports none/linear\n   234\t  - `train.epochs=20`\n   235\t  - `train.lr=0.00002`\n   236\t  - `train.retriever_lr=0.00001`\n   237\t  - `train.additional.gradient_accumulation_steps=4` \n   238\t  - `train.additional.warmup_steps=0`\n   239\t  - `train.additional.early_stop_patience=7` \n   240\t  - `train.additional.save_top_k=1`\n   241\t  - `valid.step_size=400`\n   242\t  - `valid.batch_size=4`\n   243\t  - `model_config.GeneratorModelVersion=microsoft/tapex-large`: an example of how you can change the pretrained model checkpoint for the answer generator\n   244\t  - `data_loader.additional.num_knowledge_passages=5`: an example of how you can change `K` in ITR/ITR+TableQA/OpenDomainRA-TableQA\n   245\t  - `model_config.num_beams=5`: number of beams in generation\n   246\t\n   247\t## Testing\n   248\t\n   249\t- `--test_evaluation_name nq_tables_all` this will create a folder under the experiment folder (indicated by `--experiment_name`) and save everything there. Also, in the WANDB run (run name indicated by `--experiment_name`), a new section with this name (`nq_tables_all`) will be created, and the evaluation scores will be logged into this section.\n   250\t- `--opts test.batch_size=32` \n   251\t- `--opts test.load_epoch=3825` which checkpoint to load. Note that you need to have the same experiment name\n   252\t\n   253\t\n   254\t\n   255\t# Scripts for Inner Table Retriever\n   256\t\n   257\t## Inner Table Retrieval\n   258\t**NOTE** After training, you need to run inference to generate index files that are used in TableQA + ITR. The index files will be generated in `Experiments/{experiment_name}/test/epoch{load_epoch}/`.\n   259\t\n   260\t## Main Experiments \n   261\t\n   262\t### ITR mix (intersecting columns and rows)\n   263\t\n   264\tWikiSQL train\n   265\t```\n   266\tpython src/main.py configs/wikisql/dpr_ITR_mix_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_mixed --mode train --override --opts train.batch_size=1 train.scheduler=None train.epochs=20 train.lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=200 train.additional.early_stop_patience=8 train.additional.save_top_k=3 valid.batch_size=8 test.batch_size=8 valid.step_size=200 reset=1\n   267\t```\n   268\tWikiSQL test\n   269\t```\n   270\tpython src/main.py configs/wikisql/dpr_ITR_mix_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_mixed --mode test --test_evaluation_name original_sets --opts test.batch_size=32 test.load_epoch=11604\n   271\t```\n   272\tWikiTQ test\n   273\t```\n   274\tpython src/main.py configs/wtq/dpr_ITR_mix_wtq.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_mixed --mode test --test_evaluation_name wtq_original_sets --opts test.batch_size=32 test.load_epoch=11604\n   275\t```\n   276\t\n   277\t## Additional ablation experiments \n   278\t\n   279\t### Column-wise ITR\n   280\tWikiSQL train\n   281\t```\n   282\tpython src/main.py configs/dpr_ITR_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_single_column --mode train --opts train.batch_size=1 train.scheduler=None train.epochs=20 train.lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=200 train.additional.early_stop_patience=6 train.additional.save_top_k=3 train.save_interval=200 valid.batch_size=8 test.batch_size=8 valid.step_size=200 data_loader.dummy_dataloader=0\n   283\t```\n   284\tWikiSQL test\n   285\t```\n   286\tpython src/main.py configs/dpr_ITR_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_single_column --mode test --test_evaluation_name wikisql_original_sets --opts test.batch_size=32 test.load_epoch=3801\n   287\t```\n   288\tWikiTQ test\n   289\t```\n   290\tpython src/main.py configs/wtq/dpr_ITR_column_wise_wtq.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_single_column --mode test --test_evaluation_name wtq_original_sets --opts test.batch_size=32 test.load_epoch=3801\n   291\t```\n   292\t\n   293\t### Row-wise ITR\n   294\tWikiSQL train\n   295\t```\n   296\tpython src/main.py configs/dpr_ITR_row_wise_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_single_row --mode train --opts train.batch_size=1 train.scheduler=None train.epochs=20 train.lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=200 train.additional.early_stop_patience=8 train.additional.save_top_k=3 train.save_interval=200 valid.batch_size=8 test.batch_size=8 valid.step_size=200 data_loader.dummy_dataloader=0\n   297\t```\n   298\tWikiSQL test\n   299\t```\n   300\tpython src/main.py configs/dpr_ITR_row_wise_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_single_row --mode test --test_evaluation_name wikisql_original_sets --opts test.batch_size=32 test.load_epoch=6803\n   301\t```\n   302\tWikiTQ test\n   303\t```\n   304\tpython src/main.py configs/wtq/dpr_ITR_row_wise_wikisql.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_single_row --mode test --test_evaluation_name wtq_original_sets --opts test.batch_size=32 test.load_epoch=6803\n   305\t```\n   306\t\n   307\t\n   308\t## TableQA with ITR (ITR + TaPEx)\n   309\tYou will need to change the index path in the config files. You can also change the index path by `--opts` dynamically.\n   310\t\n   311\tFor example, after running the inference for ITR-mix, you can change the config file `configs/wikisql/tapas_ITR_mix_wikisql.jsonnet` as follows:\n   312\t\n   313\t```\n   314\t// here we put the index file paths\n   315\tlocal index_files = {\n   316\t  \&quot;index_paths\&quot;: {\n   317\t    \&quot;train\&quot;: \&quot;DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_mixed/test/original_sets/step_11604/test.ITRWikiSQLDataset.train\&quot;,\n   318\t    \&quot;validation\&quot;: \&quot;DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_mixed/test/original_sets/step_11604/test.ITRWikiSQLDataset.validation\&quot;,\n   319\t    \&quot;test\&quot;: \&quot;DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_mixed/test/original_sets/step_11604/test.ITRWikiSQLDataset.test\&quot;,\n   320\t  },\n   321\t};\n   322\t```\n   323\t\n   324\tSome general notes:\n   325\t- You can set `test.load_epoch=0 model_config.GeneratorModelVersion=microsoft/tapex-large-finetuned-wikisql` to use the official checkpoint in evaluation.\n   326\t- Do make sure `test.load_epoch=` has the same number as the checkpoint. Otherwise, `model_config.GeneratorModelVersion` will be loaded.\n   327\t- Check arguments carefully before running! Especially for dangerous commands such as `--override --opts reset=1`.\n   328\t- `data_loader.additional.max_decoder_source_length=128` controls the token limit for ITR, they are 1024 for TaPEx and 512 for TaPas by default.\n   329\t- In the paper we provide different variations. Here we only show the main results with the ITR-mix strategy and the ablations with column- or row-wise ITR. Feel free to try your own configuration. You can change `model_config.ModelClass` to `ITRRagReduceMixModel`, `ITRRagAdditionRowWiseModel`, etc. Available model classes are in `models/itr_rag.py` and `models/itr_rag_reduce.py`.\n   330\t\n   331\t\n   332\t## Main Experiments \n   333\t\n   334\t### ITR mix (intersecting columns and rows)\n   335\t\n   336\tWikiSQL train\n   337\t```\n   338\tpython src/main.py configs/wikisql/tapex_ITR_mix_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name finetune_tapex_large_on_WikiSQL_with_ITR_mix_reduction_smoothing_overflow_only_original_sub_table_order --mode train --modules overflow_only original_sub_table_order shuffle_sub_table_order_in_training --override --opts train.batch_size=1 train.scheduler=linear train.epochs=10 train.lr=0.00003 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=1000 train.additional.early_stop_patience=7 train.additional.save_top_k=3 valid.step_size=1000 valid.batch_size=2 test.batch_size=2 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large data_loader.additional.num_knowledge_passages=10 reset=1\n   339\t```\n   340\tWikiSQL test\n   341\t```\n   342\tpython src/main.py configs/wikisql/tapex_ITR_mix_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name finetune_tapex_large_on_WikiSQL_with_ITR_mix_reduction_smoothing_overflow_only_original_sub_table_order --mode test --modules overflow_only original_sub_table_order --test_evaluation_name original_sets --opts test.batch_size=4 test.load_epoch=[] model_config.GeneratorModelVersion=microsoft/tapex-large model_config.ModelClass=ITRRagReduceMixModel data_loader.additional.num_knowledge_passages=1 data_loader.additional.max_decoder_source_length=1024\n   343\t```\n   344\tWikiTQ train\n   345\t```\n   346\tpython src/main.py configs/wtq/tapex_ITR_mix_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name finetune_tapex_large_on_WTQ_with_ITR_mix_reduction_smoothing_overflow_only_original_sub_table_order_K_10 --mode train --modules overflow_only original_sub_table_order --override --opts train.batch_size=1 train.scheduler=linear train.epochs=40 train.lr=0.00002 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=7 train.additional.save_top_k=3 valid.step_size=1000 valid.batch_size=2 test.batch_size=2 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large model_config.ModelClass=ITRRagReduceMixModel data_loader.additional.num_knowledge_passages=10\n   347\t```\n   348\tWikiTQ test\n   349\t```\n   350\tpython src/main.py configs/wtq/tapex_ITR_mix_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name finetune_tapex_large_on_WTQ_with_ITR_mix_reduction_smoothing_overflow_only_original_sub_table_order_K_10 --mode test --modules overflow_only original_sub_table_order --test_evaluation_name original_sets --opts test.batch_size=2 test.load_epoch=[] model_config.GeneratorModelVersion=microsoft/tapex-large-finetuned-wtq model_config.ModelClass=ITRRagReduceMixModel data_loader.additional.num_knowledge_passages=10 data_loader.additional.max_decoder_source_length=1024\n   351\t```\n   352\t\n   353\t## Additional Ablation Experiments \n   354\t\n   355\t### Column-wise ITR\n   356\tWikiSQL train\n   357\t```\n   358\tpython src/main.py configs/tapex_ITR_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name finetune_tapex_large_on_WikiSQL_with_ITR_addition_smoothing_overflow_only --mode train --modules overflow_only --opts train.batch_size=1 train.scheduler=linear train.epochs=10 train.lr=0.00003 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=1000 train.additional.early_stop_patience=5 train.additional.save_top_k=3 valid.step_size=1000 valid.batch_size=4 test.batch_size=4 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large data_loader.additional.num_knowledge_passages=5\n   359\t```\n   360\tWikiSQL test\n   361\t```\n   362\tpython src/main.py configs/wikisql/tapex_ITR_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name finetune_tapex_large_on_WikiSQL_with_ITR_addition_smoothing_overflow_only_original_sub_table_order --modules overflow_only original_sub_table_order --mode test --test_evaluation_name ITR_addition_oo_osto_official --opts test.batch_size=4 test.load_epoch=[] model_config.GeneratorModelVersion=microsoft/tapex-large  model_config.ModelClass=ITRRagModel data_loader.additional.num_knowledge_passages=5\n   363\t```\n   364\tWikiTQ train\n   365\t```\n   366\tpython src/main.py configs/wtq/tapex_ITR_column_wise_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name finetune_tapex_large_on_WTQ_with_ITR_column_wise_addition_smoothing_overflow_only_original_sub_table_order_K_10 --override --mode train --modules overflow_only original_sub_table_order --opts train.batch_size=1 train.scheduler=linear train.epochs=40 train.lr=0.00002 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=7 train.additional.save_top_k=3 valid.step_size=1000 valid.batch_size=2 test.batch_size=2 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large model_config.ModelClass=ITRRagModel data_loader.additional.num_knowledge_passages=10 reset=1\n   367\t```\n   368\tWikiTQ test\n   369\t```\n   370\tpython src/main.py configs/wtq/tapex_ITR_column_wise_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name finetune_tapex_large_on_WTQ_with_ITR_column_wise_addition_smoothing_overflow_only_original_sub_table_order_K_10 --mode test --modules overflow_only original_sub_table_order --test_evaluation_name ITR_addition_oo_osto_K_10 --opts test.batch_size=2 test.load_epoch=[] model_config.GeneratorModelVersion=microsoft/tapex-large model_config.ModelClass=ITRRagModel data_loader.additional.num_knowledge_passages=10\n   371\t```\n   372\t\n   373\t### Row-wise ITR\n   374\t\n   375\tWikiSQL train\n   376\t```\n   377\tpython src/main.py configs/tapex_ITR_row_wise_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name finetune_tapex_large_on_WikiSQL_with_ITR_row_wise_addition_smoothing_overflow_only_original_sub_table_order_K_10 --mode train --modules overflow_only original_sub_table_order force_select_last --opts train.batch_size=1 train.scheduler=linear train.epochs=10 train.lr=0.00003 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=1000 train.additional.early_stop_patience=7 train.additional.save_top_k=3 valid.step_size=1000 valid.batch_size=2 test.batch_size=2 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large model_config.ModelClass=ITRRagAdditionRowWiseModel data_loader.additional.num_knowledge_passages=10\n   378\t```\n   379\tWikiSQL test\n   380\t```\n   381\tpython src/main.py configs/tapex_ITR_row_wise_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name finetune_tapex_large_on_WikiSQL_with_ITR_row_wise_addition_smoothing_overflow_only_original_sub_table_order_K_10 --mode test --modules overflow_only original_sub_table_order --test_evaluation_name ITR_reduction_oo_osto_K_10 --opts test.batch_size=2 test.load_epoch=[] model_config.GeneratorModelVersion=microsoft/tapex-large model_config.ModelClass=ITRRagReduceRowWiseModel data_loader.additional.num_knowledge_passages=10\n   382\t```\n   383\tWikiTQ train\n   384\t```\n   385\tpython src/main.py configs/wtq/tapex_ITR_row_wise_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name finetune_tapex_large_on_WTQ_with_ITR_row_wise_addition_smoothing_overflow_only_original_sub_table_order_K_10 --mode train --modules overflow_only original_sub_table_order force_select_last --opts train.batch_size=1 train.scheduler=linear train.epochs=30 train.lr=0.00003 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=1000 train.additional.early_stop_patience=7 train.additional.save_top_k=3 valid.step_size=1000 valid.batch_size=2 test.batch_size=2 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large model_config.ModelClass=ITRRagAdditionRowWiseModel data_loader.additional.num_knowledge_passages=10\n   386\t```\n   387\tWikiTQ test\n   388\t```\n   389\tpython src/main.py configs/wtq/tapex_ITR_row_wise_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name finetune_tapex_large_on_WTQ_with_ITR_row_wise_addition_smoothing_overflow_only_original_sub_table_order_K_10 --mode test --modules overflow_only original_sub_table_order force_select_last --test_evaluation_name ITR_addition_oo_osto_fsl_K_10 --opts test.batch_size=4 test.load_epoch=[] model_config.GeneratorModelVersion=microsoft/tapex-large model_config.ModelClass=ITRRagAdditionRowWiseModel data_loader.additional.num_knowledge_passages=10\n   390\t```\n   391\t\n   392\t\n   393\t## TableQA with ITR (TaPas + ITR)\n   394\tWikiSQL test\n   395\t```\n   396\tpython src/main.py configs/wikisql/tapas_ITR_mix_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name evaluate_tapas_with_ITR_on_WikiSQL --mode test --modules overflow_only original_sub_table_order --test_evaluation_name 128tokens --opts test.batch_size=32 test.load_epoch=0 model_config.GeneratorModelVersion=google/tapas-large-finetuned-wikisql-supervised model_config.ModelClass=ITRRagReduceMixModel data_loader.additional.num_knowledge_passages=1 data_loader.additional.max_decoder_source_length=128 model_config.min_columns=1\n   397\t```\n   398\t\n   399\tWikiTQ test\n   400\t```\n   401\tpython src/main.py configs/wtq/tapas_ITR_mix_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name evaluate_tapas_with_ITR_on_WTQ_token_limit_exploration --mode test --modules overflow_only original_sub_table_order --test_evaluation_name 128tokens_official --opts test.batch_size=16 test.load_epoch=0 model_config.GeneratorModelVersion=google/tapas-large-finetuned-wtq model_config.ModelClass=ITRRagReduceMixModel data_loader.additional.num_knowledge_passages=1 data_loader.additional.max_decoder_source_length=128 model_config.min_columns=1\n   402\t```\n   403\t\n   404\t## TableQA with ITR (OmniTab + ITR)\n   405\t```\n   406\tpython src/main.py configs/wtq/tapex_ITR_mix_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name evaluate_ominitab_on_WTQ --mode test --modules overflow_only original_sub_table_order --test_evaluation_name original_sets --opts test.batch_size=2 test.load_epoch=0 model_config.GeneratorModelVersion=neulab/omnitab-large-finetuned-wtq model_config.DecoderTokenizerModelVersion=neulab/omnitab-large-finetuned-wtq model_config.ModelClass=ITRRagReduceMixModel data_loader.additional.num_knowledge_passages=10\n   407\t```\n   408\t\n   409\t## Disable ITR\n   410\t Add `--modules suppress_ITR`, for example:\n   411\t```\n   412\tpython src/main.py configs/wtq/tapas_ITR_mix_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name evaluate_tapas_with_ITR_on_WTQ_token_limit_exploration --mode test --modules overflow_only original_sub_table_order suppress_ITR --test_evaluation_name 128tokens_official_no_ITR --opts test.batch_size=16 test.load_epoch=0 model_config.GeneratorModelVersion=google/tapas-large-finetuned-wtq model_config.ModelClass=ITRRagReduceMixModel data_loader.additional.num_knowledge_passages=1 data_loader.additional.max_decoder_source_length=128 model_config.min_columns=1\n   413\t```\n   414\t\n   415\t## TableQA Baselines\n   416\t\n   417\t### TaPEx\n   418\tWikiSQL train\n   419\t```\n   420\tpython src/main.py configs/wikisql/tapex_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name finetune_tapex_large_on_WikiSQL_smoothing_0.1 --mode train --opts train.batch_size=1 train.scheduler=linear train.epochs=20 train.lr=0.00003 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=1000 train.additional.early_stop_patience=6 train.additional.save_top_k=3 train.save_interval=1000 valid.batch_size=4 test.batch_size=4 data_loader.dummy_dataloader=0 train.additional.label_smoothing_factor=0.1\n   421\t```\n   422\tWikiSQL test\n   423\t```\n   424\tpython src/main.py configs/wikisql/tapex_wikisql.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name finetune_tapex_large_on_WikiSQL_smoothing_0.1 --mode test --log_prediction_tables --test_evaluation_name original_valid_test_set --opts test.batch_size=16 test.load_epoch=[]\n   425\t```\n   426\tChange the config to `configs/wtq/tapex_wtq.jsonnet` to use WTQ instead.\n   427\t\n   428\t\n   429\t# Scripts for Open-domain TableQA with Late Interaction Models\n   430\t\n   431\t## Main Experiments \n   432\t\n   433\t### ColBERT Retrieval\n   434\tSome additional useful arguments:\n   435\t- `model_config.nbits=2`: how many bits the embeddings are quantized (compressed) into. A higher nbits will significantly increase the index size.\n   436\t\n   437\tNQ-TABLES train\n   438\t```\n   439\tpython src/main.py configs/nq_tables/colbert.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name ColBERT_NQTables_bz4_negative4_fix_doclen_full_search_NewcrossGPU --mode train --override --opts train.batch_size=6 train.scheduler=None train.epochs=1000 train.lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=10 train.additional.save_top_k=3 valid.batch_size=32 test.batch_size=32 valid.step_size=200 data_loader.dummy_dataloader=0 reset=1 model_config.num_negative_samples=4 model_config.bm25_top_k=5 model_config.bm25_ratio=0 model_config.nbits=2\n   440\t```\n   441\t\n   442\tNQ-TABLES test\n   443\t```\n   444\tpython src/main.py configs/nq_tables/colbert.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name ColBERT_NQTables_bz4_negative4_fix_doclen_full_search_NewcrossGPU --mode test --test_evaluation_name nq_tables_all --opts test.batch_size=32 test.load_epoch=5427 model_config.nbits=8\n   445\t```\n   446\t\n   447\tE2E_WTQ train\n   448\t```\n   449\tpython src/main.py configs/e2e_wtq/colbert.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 0 --experiment_name ColBERT_E2EWTQ_bz4_negative4_fix_doclen_full_search_NewcrossGPU --mode train --override --modules exhaustive_search_in_testing --opts train.batch_size=6 train.scheduler=None train.epochs=1000 train.lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=30 train.additional.save_top_k=3 valid.batch_size=32 test.batch_size=32 valid.step_size=10 data_loader.dummy_dataloader=0 reset=1 model_config.num_negative_samples=4 model_config.bm25_top_k=5 model_config.bm25_ratio=0 model_config.nbits=2\n   450\t```\n   451\t\n   452\tE2E_WTQ test\n   453\t```\n   454\tpython src/main.py configs/e2e_wtq/colbert.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name ColBERT_E2EWTQ_bz4_negative4_fix_doclen_full_search_NewcrossGPU --mode test --test_evaluation_name e2e_wtq_all --opts test.batch_size=32 test.load_epoch=300 model_config.nbits=8\n   455\t```\n   456\t\n   457\t\n   458\t### LIRAGE\n   459\t**Note**: if you are not using the index files specified in the config files, you may want to \n   460\t- change the paths in the config file; or\n   461\t- use the following to change the paths in commandline directly:\n   462\t```\n   463\t--opts model_config.QueryEncoderModelVersion=$ColBERT_NQTables_bz4_negative4_fix_doclen_full_search_NewcrossGPU/train/saved_model/step_5427 model_config.index_files.index_passages_path=ColBERT_NQTables_bz4_negative4_fix_doclen_full_search_NewcrossGPU/test/nq_tables_all/step_5427/table_dataset model_config.index_files.index_path=ColBERT_NQTables_bz4_negative4_fix_doclen_full_search_NewcrossGPU/test/nq_tables_all/step_5427/table_dataset_colbert_index model_config.index_files.embedding_path=ColBERT_NQTables_bz4_negative4_fix_doclen_full_search_NewcrossGPU/test/nq_tables_all/step_5427/item_embeddings.pkl\n   464\t```\n   465\t\n   466\t\n   467\tNQ-TABLES train\n   468\t```\n   469\tpython src/main.py configs/nq_tables/colbert_rag.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name RAG_ColBERT_NQTables_RAVQA_Approach5_add_prompt --mode train --modules add_binary_labels_as_prompt --override --opts train.batch_size=1 train.scheduler=linear train.epochs=20 train.lr=0.00002 train.retriever_lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=3 train.additional.save_top_k=1 valid.step_size=400 valid.batch_size=4 test.batch_size=4 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large data_loader.additional.num_knowledge_passages=5 model_config.num_beams=5 reset=1\n   470\t```\n   471\tNQ-TABLES test\n   472\t```\n   473\tpython src/main.py configs/nq_tables/colbert_rag.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name RAG_ColBERT_NQTables_RAVQA_Approach5_add_prompt --mode test --test_evaluation_name alternative_answers --modules add_binary_labels_as_prompt --opts test.batch_size=1 test.load_epoch=[] model_config.num_beams=5 data_loader.additional.num_knowledge_passages=5\n   474\t```\n   475\tE2E_WTQ train\n   476\t(using the officially released TaPEx does not differ much from using our own finetuned. To save steps, we just use it here. You can change it to your own finetuned TaPEx version.)\n   477\t```\n   478\tpython src/main.py configs/e2e_wtq/colbert_rag.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name RAG_ColBERT_E2EWTQ_RAVQA_Approach5_add_prompt_pretrained --modules add_binary_labels_as_prompt --mode train --override --opts train.batch_size=1 train.scheduler=none train.epochs=100 train.lr=0.000015 train.retriever_lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=3 train.additional.save_top_k=1 valid.step_size=25 valid.batch_size=4 test.batch_size=4 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large-finetuned-wtq data_loader.additional.num_knowledge_passages=5 model_config.num_beams=5 model_config.RAVQA_loss_type=Approach5 reset=1\n   479\t```\n   480\tE2E_WTQ test\n   481\t```\n   482\tpython src/main.py configs/e2e_wtq/colbert_rag.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name RAG_ColBERT_E2EWTQ_RAVQA_Approach5_add_prompt_pretrained --mode test --test_evaluation_name K5 --modules add_binary_labels_as_prompt --opts test.batch_size=1 test.load_epoch=176 model_config.num_beams=5 data_loader.additional.num_knowledge_passages=5\n   483\t```\n   484\t\n   485\t## Additional Ablation Experiments \n   486\t\n   487\t### Dense Passage Retrieval (DPR)\n   488\t\n   489\tSome useful arguments\n   490\t- `--modules negative_samples_across_gpus`: sharing negative samples across GPUs\n   491\t- `--modules exhaustive_search_in_testing`: use exhaustive search in testing, but this is significantly slower than building HNSW index. HNSW offers faster dynamic search later\n   492\t- `--opts model_config.bm25_ratio=0 model_config.bm25_top_k=3`\n   493\t  - `ratio=0`: no bm25 mined negative examples are used\n   494\t  - `bm25_top_k=K`: find negative examples in top-K bm25 mined examples. Note that this value should be large enough when `model_config.num_negative_samples` is large so that enough examples can be found.\n   495\t  - Note: this didn't improve the performance at the end. So it is disabled now.\n   496\t\n   497\t\n   498\tNQ-TABLES train\n   499\t```\n   500\tpython src/main.py configs/nq_tables/dpr.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name DPR_NQTables_train_bz8_gc_4_crossGPU --mode train --override --modules negative_samples_across_gpus exhaustive_search_in_testing --opts train.batch_size=8 train.scheduler=None train.epochs=1000 train.lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=10 train.additional.save_top_k=3 valid.batch_size=32 test.batch_size=32 valid.step_size=200 data_loader.dummy_dataloader=0 reset=1 model_config.num_negative_samples=4 model_config.bm25_ratio=0 model_config.bm25_top_k=3\n   501\t```\n   502\t\n   503\tNQ-TABLES test\n   504\t```\n   505\tpython src/main.py configs/nq_tables/dpr.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name DPR_NQTables_train_bz8_gc_4_crossGPU --mode test --test_evaluation_name nq_tables_all --opts test.batch_size=32 test.load_epoch=[]\n   506\t```\n   507\t\n   508\tE2E_WTQ train\n   509\t\n   510\t(Note that loading a pre-trained DPR checkpoint does not improve the performance much. If you don't have a checkpoint pre-trained on NQ-TABLES, simply drop `model_config.QueryEncoderModelVersion` and `model_config.ItemEncoderModelVersion`.)\n   511\t\n   512\t```\n   513\tpython src/main.py configs/e2e_wtq/dpr.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name DPR_E2EWTQ_train_bz8_gc_4_neg4 --mode train --override --modules exhaustive_search_in_testing --opts train.batch_size=8 train.scheduler=None train.epochs=300 train.lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=100 train.additional.save_top_k=3 valid.batch_size=32 test.batch_size=32 valid.step_size=10 data_loader.dummy_dataloader=0 reset=1 model_config.num_negative_samples=4 model_config.QueryEncoderModelVersion=/wd/Experiments/DPR_NQTables_train_bz8_gc_4_crossGPU/train/saved_model/step_2039/query_encoder model_config.ItemEncoderModelVersion=/wd/Experiments/DPR_NQTables_train_bz8_gc_4_crossGPU/train/saved_model/step_2039/item_encoder model_config.bm25_top_k=5 model_config.bm25_ratio=0\n   514\t```\n   515\t\n   516\tE2E_WTQ test\n   517\t```\n   518\tpython src/main.py configs/e2e_wtq/dpr.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name DPR_E2EWTQ_train_bz8_gc_4_neg4 --mode test --test_evaluation_name e2e_wtq_all --opts test.batch_size=32 test.load_epoch=480\n   519\t```\n   520\t\n   521\t\n   522\t### DPR + RAGE \n   523\tSome settings:\n   524\t- `--modules add_binary_labels_as_prompt`: add binary relevance tokens in training and in testing. Note: must be enabled both in training and testing.\n   525\t- `nq_tables/rag.jsonnet`: joint training of retriever and reader; `frozen_rag.jsonnet`: freeze the retriever during training.\n   526\t\n   527\tNQ-TABLES train\n   528\t```\n   529\tpython src/main.py configs/nq_tables/rag.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name RAG_NQTables_RAVQA_loss_approach5_add_prompt --mode train --modules add_binary_labels_as_prompt --override --opts train.batch_size=1 train.scheduler=linear train.epochs=20 train.lr=0.00002 train.retriever_lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=3 train.additional.save_top_k=1 valid.step_size=400 valid.batch_size=4 test.batch_size=4 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large data_loader.additional.num_knowledge_passages=5 model_config.num_beams=5 model_config.RAVQA_loss_type=Approach5 reset=1\n   530\t```\n   531\t\n   532\tNQ-TABLES test\n   533\t```\n   534\tpython src/main.py configs/nq_tables/rag.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name RAG_NQTables_RAVQA_loss_approach5_add_prompt --mode test --test_evaluation_name official_sets --modules add_binary_labels_as_prompt --opts test.batch_size=1 test.load_epoch=[] model_config.num_beams=5 data_loader.additional.num_knowledge_passages=5\n   535\t```\n   536\t\n   537\t\n   538\tE2E_WTQ train\n   539\t```\n   540\tpython src/main.py configs/e2e_wtq/rag.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name RAG_E2EWTQ_RAVQA_Approach5_add_prompt --mode train --modules add_binary_labels_as_prompt --override --opts train.batch_size=1 train.scheduler=none train.epochs=100 train.lr=0.000015 train.retriever_lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=3 train.additional.save_top_k=-1 valid.step_size=25 valid.batch_size=4 test.batch_size=4 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large-finetuned-wtq data_loader.additional.num_knowledge_passages=5 model_config.num_beams=5 model_config.RAVQA_loss_type=Approach5 reset=1\n   541\t```\n   542\t\n   543\tE2E_WTQ test\n   544\t```\n   545\tpython src/main.py configs/e2e_wtq/rag.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name RAG_E2EWTQ_RAVQA_Approach5_add_prompt --mode test --test_evaluation_name K5 --modules add_binary_labels_as_prompt --opts test.batch_size=1 test.load_epoch=126 model_config.num_beams=5 data_loader.additional.num_knowledge_passages=5\n   546\t```\n   547\t\n   548\t## Miscellaneous  \n   549\t\n   550\t### Difference w.r.t. the official ColBERT repository\n   551\tWe have made several changes to the ColBERT codebase so that it can be run and integrated into our framework.\n   552\t- The original code uses `total_visible_gpus` to determine if we are to use gpus. However, in dynamic retrieval, we'd like to use cpu for retrieval only, while keeping gpus visible to our main training framework. Therefore, we modified `colbert/indexing/codecs/residual.py` to add an argument `disable_gpu`. Similarly, `self.use_gpu` is added to `colbert/search/index_loader.py`. `colbert/searcher.py` reads `initial_config.total_visible_gpus = config.total_visible_gpus` from the pass-in config.\n   553\t- `colbert/indexing/collection_indexer.py`: we commented out `self.config.help()` which generates redundant information flooding the terminal.\n   554\t- `colbert/modeling/colbert.py`: its original negative samples are only shared across batches in one device. We added support for sharing negative samples in all batches across all GPUs. We also re-wrote the original in-batch negative sampling loss with a cleaner version.\n   555\t- `colbert/modeling/tokenization/doc_tokenization.py`: the padding strategy is changed from `longest` to `max_length`. This is because our tables are typically long, and we set `max_length=512`.\n   556\t- Other issues that blocked running\n   557\t  - `colbert/search/index_storage.py`: `approx_scores = torch.cat(approx_scores, dim=0).float()`\n   558\t  - `colbert/search/strided_tensor.py`: `pids = pids.cpu()` is necessary. Move them to GPU afterwards.\n   559\t\n   560\t\n   561\t## Security\n   562\t\n   563\tSee [CONTRIBUTING](CONTRIBUTING.md#security-issue-notifications) for more information.\n   564\t\n   565\t## License Summary\n   566\t\n   567\tThe documentation is made available under the Creative Commons Attribution-ShareAlike 4.0 International License. See the LICENSE file.\n   568\t\n   569\tThe sample code within this documentation is made available under the MIT-0 license. See the LICENSE-SAMPLECODE file.\n   570\t\nTotal lines in file: 570\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_01NF2T3egqvfu1RXNTW3RKvJ&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/Documents/Code/Experiment/robust-tableqa-main&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/Documents/Code/Experiment/robust-tableqa-main&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-28T15:53:00.372Z&quot;,&quot;response_text&quot;:&quot;现在让我查看项目的源代码结构来更好地了解实现细节：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;44538700-bd34-440d-a3d2-c17a7a4fc7f3&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_018WkawMMRXFZro5zQbo3wAx&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;configs\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;让我查看一些配置文件来了解项目的配置结构：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in src, excluding hidden items:\nsrc/ColBERT\nsrc/data_loader_manager\nsrc/main.py\nsrc/models\nsrc/trainers\nsrc/utils\nsrc/ColBERT/LICENSE\nsrc/ColBERT/LoTTE.md\nsrc/ColBERT/README.md\nsrc/ColBERT/baleen\nsrc/ColBERT/colbert\nsrc/ColBERT/conda_env.yml\nsrc/ColBERT/conda_env_cpu.yml\nsrc/ColBERT/docs\nsrc/ColBERT/setup.py\nsrc/ColBERT/utility\nsrc/data_loader_manager/__init__.py\nsrc/data_loader_manager/data_loader_for_tableqa.py\nsrc/data_loader_manager/data_loader_wrapper.py\nsrc/data_loader_manager/datasets\nsrc/data_loader_manager/module_parser.py\nsrc/models/dpr\nsrc/models/rag\nsrc/models/tapas\nsrc/trainers/ColBERT_executor.py\nsrc/trainers/DPR_executor.py\nsrc/trainers/ITR_DPR_executor.py\nsrc/trainers/ITR_rag_executor.py\nsrc/trainers/RAG_executor.py\nsrc/trainers/__init__.py\nsrc/trainers/base_executor.py\nsrc/trainers/metrics_processors.py\nsrc/trainers/tapas_executor.py\nsrc/trainers/tapex_executor.py\nsrc/utils/cache_system.py\nsrc/utils/collect_env.py\nsrc/utils/color_logging.py\nsrc/utils/config_system.py\nsrc/utils/cuda_stats.py\nsrc/utils/dirs.py\nsrc/utils/metrics_log_callback.py\nsrc/utils/seed.py\nsrc/utils/tapas_metrics_utils.py\nsrc/utils/tapas_utils.py\nsrc/utils/text_utils.py\nsrc/utils/wikisql_utils.py\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_01Hic3AC6RneFeMLPdJ2mFdC&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/Documents/Code/Experiment/robust-tableqa-main&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/Documents/Code/Experiment/robust-tableqa-main&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-28T15:53:05.167Z&quot;,&quot;response_text&quot;:&quot;让我查看一些配置文件来了解项目的配置结构：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;cffbc6d8-cc29-4cb6-8617-a8e0db280d08&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:0,&quot;content&quot;:&quot;基于我对项目结构和README文件的分析，我来为您总结这个项目：\n\n## 项目总结：Robust Table Question Answering\n\n### 项目概述\n这是一个用于训练和评估基于Transformer的鲁棒表格问答（Table QA）模型的代码库，包含了两篇ACL 2023论文的实现：\n\n1. **Inner Table Retriever (ITR)** - 内表检索器\n2. **LI-RAGE** - 基于后期交互的检索增强生成模型\n\n### 核心技术方案\n\n#### 1. Inner Table Retriever (ITR)\n- **目标**：处理长表格的通用方法，通过提取子表格来保留与问题最相关的信息\n- **特点**：\n  - 可以轻松集成到现有系统中提高准确性\n  - 支持三种策略：列级（column-wise）、行级（row-wise）、混合（mix）\n  - 基于Dense Passage Retrieval (DPR)实现\n\n#### 2. LI-RAGE (Late Interaction Retrieval Augmented Generation)\n- **目标**：开放域表格问答框架\n- **核心创新**：\n  - 使用后期交互模型（基于ColBERT）实现问题和表格嵌入的细粒度交互\n  - 检索器和阅读器的联合训练\n  - 在生成答案前添加二进制相关性标记，用于推理时过滤不可靠的表格\n\n### 技术架构\n\n#### 框架结构\n- **主入口**：`main.py` - 加载配置文件并启动训练/测试\n- **数据加载器**：处理数据集加载和预处理\n- **模型训练器**：基于PyTorch Lightning的训练模块\n- **模型实现**：包含各种TableQA模型的实现\n\n#### 支持的基线模型\n- **TAPAS** (Huggingface)\n- **TAPEX** (Huggingface) \n- **OmniTab** (Huggingface)\n\n#### 检索模型\n- **Dense Passage Retrieval (DPR)**\n- **ColBERT** (后期交互模型)\n\n### 数据集支持\n- **WikiSQL** - 结构化查询数据集\n- **WikiTableQuestions (WTQ)** - 自然语言表格问答\n- **NQ-Tables** - 自然问题表格版本\n- **E2E-WTQ** - 端到端WikiTableQuestions\n\n### 配置系统\n- 使用**Jsonnet**实现配置继承\n- 支持命令行参数覆盖\n- 模块化的输入/输出处理管道\n\n### 关键特性\n\n#### ModuleParser\n- 装饰器类，帮助将数据解析为模型使用的特征\n- 支持问题输入、表格输入、相似度输出等模块\n\n#### MetricsProcessor  \n- 支持多种评估指标\n- 包括精确匹配、检索指标等\n\n### 实验环境\n- **硬件要求**：8个V100/A100 GPU集群\n- **框架依赖**：\n  - PyTorch + PyTorch Lightning\n  - Huggingface Transformers\n  - FAISS (用于向量检索)\n  - Weights &amp; Biases (实验跟踪)\n\n### 主要贡献\n1. **ITR方法**：有效处理长表格，可集成到现有TableQA系统\n2. **后期交互检索**：更精细的问题-表格匹配\n3. **联合训练**：检索器和生成器的端到端优化\n4. **可靠性过滤**：通过二进制标记判断表格相关性\n\n这个项目为表格问答任务提供了一个完整的研究和实验平台，特别在处理长表格和开放域问答方面有显著创新。\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in configs, excluding hidden items:\nconfigs/base_env.jsonnet\nconfigs/e2e_wtq\nconfigs/nq_tables\nconfigs/wikisql\nconfigs/wtq\nconfigs/e2e_wtq/colbert.jsonnet\nconfigs/e2e_wtq/colbert_rag.jsonnet\nconfigs/e2e_wtq/dpr.jsonnet\nconfigs/e2e_wtq/frozen_colbert_rag.jsonnet\nconfigs/e2e_wtq/frozen_rag.jsonnet\nconfigs/e2e_wtq/rag.jsonnet\nconfigs/nq_tables/colbert.jsonnet\nconfigs/nq_tables/colbert_rag.jsonnet\nconfigs/nq_tables/dpr.jsonnet\nconfigs/nq_tables/frozen_colbert_rag.jsonnet\nconfigs/nq_tables/frozen_rag.jsonnet\nconfigs/nq_tables/rag.jsonnet\nconfigs/wikisql/dpr_ITR_column_wise_wikisql.jsonnet\nconfigs/wikisql/dpr_ITR_column_wise_wikisql_extreme.jsonnet\nconfigs/wikisql/dpr_ITR_mix_wikisql.jsonnet\nconfigs/wikisql/dpr_ITR_mix_wikisql_extreme.jsonnet\nconfigs/wikisql/dpr_ITR_row_wise_wikisql.jsonnet\nconfigs/wikisql/dpr_ITR_row_wise_wikisql_extreme.jsonnet\nconfigs/wikisql/tapas_ITR_mix_wikisql.jsonnet\nconfigs/wikisql/tapex_ITR_mix_wikisql.jsonnet\nconfigs/wikisql/tapex_ITR_mix_wikisql_extreme.jsonnet\nconfigs/wikisql/tapex_ITR_reduce_wikisql.jsonnet\nconfigs/wikisql/tapex_ITR_row_wise_wikisql.jsonnet\nconfigs/wikisql/tapex_ITR_row_wise_wikisql_extreme.jsonnet\nconfigs/wikisql/tapex_ITR_wikisql.jsonnet\nconfigs/wikisql/tapex_ITR_wikisql_extreme.jsonnet\nconfigs/wikisql/tapex_wikisql.jsonnet\nconfigs/wikisql/tapex_wikisql_extreme.jsonnet\nconfigs/wtq/dpr_ITR_column_wise_wtq.jsonnet\nconfigs/wtq/dpr_ITR_mix_wtq.jsonnet\nconfigs/wtq/dpr_ITR_row_wise_wtq.jsonnet\nconfigs/wtq/tapas_ITR_mix_wtq.jsonnet\nconfigs/wtq/tapas_base.jsonnet\nconfigs/wtq/tapas_use_original_data.jsonnet\nconfigs/wtq/tapex_ITR_column_wise_wtq.jsonnet\nconfigs/wtq/tapex_ITR_mix_wtq.jsonnet\nconfigs/wtq/tapex_ITR_row_wise_wtq.jsonnet\nconfigs/wtq/tapex_base.jsonnet\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_018WkawMMRXFZro5zQbo3wAx&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/Documents/Code/Experiment/robust-tableqa-main&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/Documents/Code/Experiment/robust-tableqa-main&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-28T15:53:10.484Z&quot;,&quot;response_text&quot;:&quot;基于我对项目结构和README文件的分析，我来为您总结这个项目：\n\n## 项目总结：Robust Table Question Answering\n\n### 项目概述\n这是一个用于训练和评估基于Transformer的鲁棒表格问答（Table QA）模型的代码库，包含了两篇ACL 2023论文的实现：\n\n1. **Inner Table Retriever (ITR)** - 内表检索器\n2. **LI-RAGE** - 基于后期交互的检索增强生成模型\n\n### 核心技术方案\n\n#### 1. Inner Table Retriever (ITR)\n- **目标**：处理长表格的通用方法，通过提取子表格来保留与问题最相关的信息\n- **特点**：\n  - 可以轻松集成到现有系统中提高准确性\n  - 支持三种策略：列级（column-wise）、行级（row-wise）、混合（mix）\n  - 基于Dense Passage Retrieval (DPR)实现\n\n#### 2. LI-RAGE (Late Interaction Retrieval Augmented Generation)\n- **目标**：开放域表格问答框架\n- **核心创新**：\n  - 使用后期交互模型（基于ColBERT）实现问题和表格嵌入的细粒度交互\n  - 检索器和阅读器的联合训练\n  - 在生成答案前添加二进制相关性标记，用于推理时过滤不可靠的表格\n\n### 技术架构\n\n#### 框架结构\n- **主入口**：`main.py` - 加载配置文件并启动训练/测试\n- **数据加载器**：处理数据集加载和预处理\n- **模型训练器**：基于PyTorch Lightning的训练模块\n- **模型实现**：包含各种TableQA模型的实现\n\n#### 支持的基线模型\n- **TAPAS** (Huggingface)\n- **TAPEX** (Huggingface) \n- **OmniTab** (Huggingface)\n\n#### 检索模型\n- **Dense Passage Retrieval (DPR)**\n- **ColBERT** (后期交互模型)\n\n### 数据集支持\n- **WikiSQL** - 结构化查询数据集\n- **WikiTableQuestions (WTQ)** - 自然语言表格问答\n- **NQ-Tables** - 自然问题表格版本\n- **E2E-WTQ** - 端到端WikiTableQuestions\n\n### 配置系统\n- 使用**Jsonnet**实现配置继承\n- 支持命令行参数覆盖\n- 模块化的输入/输出处理管道\n\n### 关键特性\n\n#### ModuleParser\n- 装饰器类，帮助将数据解析为模型使用的特征\n- 支持问题输入、表格输入、相似度输出等模块\n\n#### MetricsProcessor  \n- 支持多种评估指标\n- 包括精确匹配、检索指标等\n\n### 实验环境\n- **硬件要求**：8个V100/A100 GPU集群\n- **框架依赖**：\n  - PyTorch + PyTorch Lightning\n  - Huggingface Transformers\n  - FAISS (用于向量检索)\n  - Weights &amp; Biases (实验跟踪)\n\n### 主要贡献\n1. **ITR方法**：有效处理长表格，可集成到现有TableQA系统\n2. **后期交互检索**：更精细的问题-表格匹配\n3. **联合训练**：检索器和生成器的端到端优化\n4. **可靠性过滤**：通过二进制标记判断表格相关性\n\n这个项目为表格问答任务提供了一个完整的研究和实验平台，特别在处理长表格和开放域问答方面有显著创新。\n&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-b178ac62-c3ad-4657-8ee0-da4f53e77d2e&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-2ec5a5be-527c-463a-9e29-de935ca6da72&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-bbd7df5e-c026-4fe6-9534-b988fee1dc2b&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-ea492cea-46e5-4939-8096-4126f7186984&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-11a05817-b592-4a40-9c6d-57e0a53a809a&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{&quot;bdca28a4-6a1c-4468-9858-6736f7a6daf6;toolu_01P5U4bVJy5rjLdZ4L4uCXJk&quot;:{&quot;requestId&quot;:&quot;bdca28a4-6a1c-4468-9858-6736f7a6daf6&quot;,&quot;toolUseId&quot;:&quot;toolu_01P5U4bVJy5rjLdZ4L4uCXJk&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in ., excluding hidden items:\n./CONTRIBUTING.md\n./LICENSE\n./LICENSE-SAMPLECODE\n./LICENSE-SUMMARY\n./README.md\n./configs\n./src\n./configs/base_env.jsonnet\n./configs/e2e_wtq\n./configs/nq_tables\n./configs/wikisql\n./configs/wtq\n./src/ColBERT\n./src/data_loader_manager\n./src/main.py\n./src/models\n./src/trainers\n./src/utils\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;68164c93-ae3a-4583-8af3-1571f746c08d;toolu_01NF2T3egqvfu1RXNTW3RKvJ&quot;:{&quot;requestId&quot;:&quot;68164c93-ae3a-4583-8af3-1571f746c08d&quot;,&quot;toolUseId&quot;:&quot;toolu_01NF2T3egqvfu1RXNTW3RKvJ&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on README.md:\n     1\t## Robust Table Question Answering\n     2\t\n     3\tCode for training and evaluation the transformer-based robust Table QA models introduced in the following ACL 2023 papers:\n     4\t\n     5\t&lt;div align=\&quot;center\&quot;&gt;    \n     6\t \n     7\t### An &lt;u&gt;I&lt;/u&gt;nner &lt;u&gt;T&lt;/u&gt;able &lt;u&gt;R&lt;/u&gt;etriever for Robust Table Question Answering\n     8\t\n     9\t[![Paper](https://img.shields.io/badge/Paper-Amazon_Science-orange)](https://www.amazon.science/publications/li-rage-late-interaction-retrieval-augmented-generation-with-explicit-signals-for-open-domain-table-question-answering)\n    10\t[![Conference](https://img.shields.io/badge/Conference-ACL--2023-blue)](https://2023.aclweb.org/)\n    11\t[![License: CC BY-NC 4.0](https://img.shields.io/badge/License-CC%20BY--NC%204.0-lightgrey.svg)](https://creativecommons.org/licenses/by-nc/4.0/)\n    12\t\n    13\t&lt;/div&gt;\n    14\t\n    15\tInner Table Retriever (ITR) is a general-purpose approach for handling long tables in TableQA \n    16\tthat extracts sub-tables to preserve the most relevant information for a question. \n    17\tITR can be easily integrated into existing systems to improve their accuracy achieve state-of-the-art results.\n    18\t\n    19\tIf you find our paper, code or framework useful, please put a link to this repo and reference this work in your paper:\n    20\t\n    21\t```\n    22\t@Inproceedings{Lin2023,\n    23\t author = {Weizhe Lin and Rexhina Blloshmi  and Bill Byrne and Adrià de Gispert and Gonzalo Iglesias},\n    24\t title = {An inner table retriever for robust table question answering},\n    25\t year = {2023},\n    26\t url = {https://www.amazon.science/publications/an-inner-table-retriever-for-robust-table-question-answering},\n    27\t booktitle = {ACL 2023},\n    28\t}\n    29\t```\n    30\t\n    31\tFor more info and details on how to install/run check [Scripts for Inner Table Retriever](#scripts-for-inner-table-retriever) in the [Codebase](#codebase) Section below. \n    32\t\n    33\t&lt;hr&gt; \n    34\t\n    35\t&lt;div align=\&quot;center\&quot;&gt;\n    36\t \n    37\t### LI-RAGE: &lt;u&gt;L&lt;/u&gt;ate &lt;u&gt;I&lt;/u&gt;nteraction &lt;u&gt;R&lt;/u&gt;etrieval &lt;u&gt;A&lt;/u&gt;ugmented &lt;u&gt;G&lt;/u&gt;eneration with &lt;u&gt;E&lt;/u&gt;xplicit Signals for Open-Domain Table Question Answering\n    38\t\n    39\t[![Paper](https://img.shields.io/badge/Paper-Amazon_Science-orange)](https://www.amazon.science/publications/li-rage-late-interaction-retrieval-augmented-generation-with-explicit-signals-for-open-domain-table-question-answering)\n    40\t[![Conference](https://img.shields.io/badge/Conference-ACL_2023-red)](https://2023.aclweb.org/)\n    41\t[![License: CC BY-NC 4.0](https://img.shields.io/badge/License-CC%20BY--NC%204.0-lightgrey.svg)](https://creativecommons.org/licenses/by-nc/4.0/)\n    42\t\n    43\t&lt;/div&gt;\n    44\tLI-RAGE is a framework for open-domain TableQA which addresses several limitations thanks to: \n    45\t\n    46\t1) applying late interaction models which enforce a finer-grained interaction between question and table embeddings at retrieval time. \n    47\t2) incorporating a joint training scheme of the retriever and reader with explicit table-level signals, and \n    48\t3) embedding a binary relevance token as a prefix to the answer generated by the reader, \n    49\tso we can determine at inference time whether the table used to answer the question is reliable and filter accordingly. \n    50\tThe combined strategies set a new state-to-the-art performance on two public open-domain TableQA datasets.\n    51\t\n    52\t\n    53\t\n    54\tIf you find our paper, code or framework useful, please put a link to this repo and reference this work in your paper:\n    55\t\n    56\t```\n    57\t@Inproceedings{Lin2023,\n    58\t author = {Weizhe Lin and Rexhina Blloshmi and Bill Byrne and Adrià de Gispert and Gonzalo Iglesias},\n    59\t title = {LI-RAGE: Late interaction retrieval augmented generation with explicit signals for open-domain table question answering},\n    60\t year = {2023},\n    61\t url = {https://www.amazon.science/publications/li-rage-late-interaction-retrieval-augmented-generation-with-explicit-signals-for-open-domain-table-question-answering},\n    62\t booktitle = {ACL 2023},\n    63\t}\n    64\t```\n    65\t\n    66\tFor more info and details on how to install/run check [Scripts for Open-domain TableQA with Late Interaction Models](#scripts-for-open-domain-tableqa-with-late-interaction-models) in the [Codebase](#codebase) Section below.\n    67\t\n    68\t&lt;hr&gt;\n    69\t\n    70\t## Codebase \n    71\t\n    72\t- [Codebase Structure](#codebase-structure)\n    73\t  - [3rd-party Open-Source Tools](#3rd-party-open-source-tools)\n    74\t  - [Overview](#overview)\n    75\t    - [Structure](#structure)\n    76\t    - [Configs](#configs)\n    77\t    - [ModuleParser](#moduleparser)\n    78\t    - [MetricsProcessor](#metricsprocessor)\n    79\t- [Environment](#environment)\n    80\t- [Data](#data)\n    81\t- [Useful Command-line Arguments](#useful-command-line-arguments)\n    82\t  - [Universal](#universal)\n    83\t  - [Training](#training)\n    84\t  - [Testing](#testing)\n    85\t- [Scripts for Inner Table Retriever](#scripts-for-inner-table-retriever)\n    86\t  - [Inner Table Retrieval](#inner-table-retrieval)\n    87\t    - [Main Experiments](#main-experiments)\n    88\t    - [Additional ablation experiments](#additional-ablation-experiments)\n    89\t  - [TableQA with ITR (TaPEx + ITR)](#tableqa-with-itr-tapex--itr)\n    90\t    - [Main Experiments](#main-experiments-1)\n    91\t    - [Additional ablation experiments](#additional-ablation-experiments-1)\n    92\t  - [TableQA with ITR (TaPas + ITR)](#tableqa-with-itr-tapas--itr)\n    93\t  - [TableQA with ITR (OmniTab + ITR)](#tableqa-with-itr-omnitab--itr)\n    94\t  - [Disable ITR](#disable-itr)\n    95\t  - [TableQA Baselines](#tableqa-baselines)\n    96\t- [Scripts for Open-domain TableQA with Late Interaction Models](#scripts-for-open-domain-tableqa-with-late-interaction-models)\n    97\t  - [Main Experiments](#main-experiments-2)\n    98\t  - [Additional ablation experiments](#additional-ablation-experiments-2)\n    99\t\n   100\tThe code base is created by Weizhe Lin (<EMAIL>), during his internship as an applied scientist of Alexa AI. \n   101\tThis code base contains:\n   102\t- TableQA Baseline Systems\n   103\t    - TAPAS (Huggingface)\n   104\t    - TAPEX (Huggingface)\n   105\t    - OmniTab (Huggingface)\n   106\t- Inner Table Retrieval (ITR)\n   107\t    - Inner Table Retriever based on Dense Passage Retrieval (DPR)\n   108\t    - TableQA systems with ITR\n   109\t-  Late Interaction Models for Retrieval Augmented TableQA Systems\n   110\t    - Late Interaction Table Retriever based on ColBERT\n   111\t    - Retrieval Augmented TableQA Models (TaPEx) with Late Interaction Retriever\n   112\t    - Dense Table Retriever (DPR)\n   113\t    - Retrieval Augmented TableQA Models (TaPEx) with DPR\n   114\t\n   115\t\n   116\t# Codebase Structure\n   117\t\n   118\t## 3rd-party Open-Source Tools\n   119\tIn this codebase, several open-source tools are used, including:\n   120\t- Weights and Biases\n   121\t- Pytorch-lightning\n   122\t- Pytorch\n   123\t- Pytorch-scatter\n   124\t- Huggingface-transformers\n   125\t- Other open-source tools\n   126\t  - Experiment framework is built upon [RAVQA](https://github.com/LinWeizheDragon/Retrieval-Augmented-Visual-Question-Answering)\n   127\t\n   128\t\n   129\t## Overview\n   130\tThe training and testing are backboned by pytorch-lightning. The pre-trained Transformer models are from Huggingface-transformers. The training platform is Pytorch.\n   131\t\n   132\t### Structure\n   133\tThe framework consists of:\n   134\t\n   135\t1. **main.py**: the entry point to the main program. It loads a config file and override some entries with command-line arguments. It initialises a data loader wrapper, a model trainer, and a pytorch-lightning trainer to execute training and testing.\n   136\t2. **Data Loader Wrapper**: it loads the data according to `data_loader.dataset_modules` defined in config files. A data module called `LoadDataLoaders` is used to create pytorch dataloaders from the data.\n   137\t3. **Datasets**: they are automatically loaded by the data loader wrapper. `.collate_fn` is defined to collate the data. An decorator class `ModuleParser` is used to help generate the training inputs. This decorator class generates input dict according to configs (`config.model_config.input_modules/decorder_input_modules/output_modules`).\n   138\t4. **Model Trainers**: a pytorch-lightning `LightningModule` instance. It defines training/testing behaviors (training steps, optimizers, schedulers, logging, checkpointing, and so on). It initialises the model being trained at `self.model`.\n   139\t5. **Models**: pytorch `nn.Modules` models.\n   140\t\n   141\t### Configs\n   142\tThe configuration is achieved with `jsonnet`. It enables inheritance of config files. For example, `wtq/tapex_ITR_mix_wtq.jsonnet` override its configs to `wtq/tapex_ITR_column_wise_wtq.jsonnet`, which again inherits from `base_env.jsonnet` where most of common configurations are defined.\n   143\t\n   144\tBy including the corresponding key:value pair in the config file, overriding can be easily performed.\n   145\t\n   146\t### ModuleParser\n   147\tA decorator class that helps to parse data into features that are used by models.\n   148\t\n   149\tAn example taken from ITR is shown below:\n   150\t```\n   151\t\&quot;input_modules\&quot;: {\n   152\t    \&quot;module_list\&quot;:[\n   153\t    {\&quot;type\&quot;: \&quot;QuestionInput\&quot;,  \&quot;option\&quot;: \&quot;default\&quot;, \n   154\t                \&quot;separation_tokens\&quot;: {\&quot;start\&quot;: \&quot;\&quot;, \&quot;end\&quot;: \&quot;\&quot;}},\n   155\t    ],\n   156\t    \&quot;postprocess_module_list\&quot;: [\n   157\t    {\&quot;type\&quot;: \&quot;PostProcessInputTokenization\&quot;, \&quot;option\&quot;: \&quot;default\&quot;},\n   158\t    ],\n   159\t},\n   160\t\&quot;decoder_input_modules\&quot;: {\n   161\t    \&quot;module_list\&quot;:[\n   162\t    {\&quot;type\&quot;: \&quot;TextBasedTableInput\&quot;,  \&quot;option\&quot;: \&quot;default\&quot;,\n   163\t                \&quot;separation_tokens\&quot;: {\&quot;header_start\&quot;: \&quot;&lt;HEADER&gt;\&quot;, \&quot;header_sep\&quot;: \&quot;&lt;HEADER_SEP&gt;\&quot;, \&quot;header_end\&quot;: \&quot;&lt;HEADER_END&gt;\&quot;, \&quot;row_start\&quot;: \&quot;&lt;ROW&gt;\&quot;, \&quot;row_sep\&quot;: \&quot;&lt;ROW_SEP&gt;\&quot;, \&quot;row_end\&quot;: \&quot;&lt;ROW_END&gt;\&quot;}},\n   164\t    ],\n   165\t    \&quot;postprocess_module_list\&quot;: [\n   166\t    {\&quot;type\&quot;: \&quot;PostProcessDecoderInputTokenization\&quot;, \&quot;option\&quot;: \&quot;default\&quot;},\n   167\t    ],\n   168\t},\n   169\t\&quot;output_modules\&quot;: {\n   170\t    \&quot;module_list\&quot;:[\n   171\t    {\&quot;type\&quot;: \&quot;SimilarityOutput\&quot;, \&quot;option\&quot;: \&quot;default\&quot;},\n   172\t    ],\n   173\t    \&quot;postprocess_module_list\&quot;: [\n   174\t    {\&quot;type\&quot;: \&quot;PostProcessConcatenateLabels\&quot;, \&quot;option\&quot;: \&quot;default\&quot;},\n   175\t    ],\n   176\t},\n   177\t```\n   178\twhich first run input modules in the order defined in `input_modules`, and then the postprocessing unit `PostProcessInputTokenization` is used to tokenize the input into `input_ids` and `input_attention_mask`.\n   179\t\n   180\tSimilarly, `decoder_input_modules` (in the ITR, decoder input is used as the input to the item encoder. The naming is slightly confusing but feel free to change it, though not deemed necessary) generates `item_input_ids` and `item_input_attention_mask`. `output_modules` generates the labels for ITR training.\n   181\t\n   182\tBy defining new functions in `ModuleParser`, e.g. `self.TextBasedVisionInput`, a new behavior can be easily introduced to transform modules into training features.\n   183\t\n   184\t### MetricsProcessor\n   185\tThe following entries in config file `test.metrics` define the metrics to compute in validation and testing. Each module uploads `log_dict` with `metrics_name: metrics_value` which can be processed in trainers conveniently.\n   186\t```\n   187\t\&quot;metrics\&quot;: [\n   188\t    {'name': 'compute_exact_match'},\n   189\t    {'name': 'compute_retrieval_metrics'},\n   190\t],\n   191\t```\n   192\t\n   193\t\n   194\t# Environment\n   195\t```\n   196\tconda create -n tableqa python=3.8\n   197\tconda activate tableqa\n   198\tpip install torch==1.10.1+cu111 torchvision==0.11.2+cu111 torchaudio==0.10.1 -f https://download.pytorch.org/whl/torch_stable.html\n   199\tpip install torch-scatter -f https://data.pyg.org/whl/torch-1.10.1+cu111.html --force-reinstall --no-cache-dir --no-index\n   200\tpip install transformers==4.22.1 datasets==2.6.1\n   201\tpip install jsonnet\n   202\tpip install easydict tqdm pytorch-lightning==1.8.2 tfrecord frozendict\n   203\tpip install wandb==0.13.4\n   204\tconda install -c pytorch faiss-gpu -y\n   205\tpip install bitarray spacy ujson gitpython\n   206\tpip install ninja\n   207\tpip install absl-py tensorboard\n   208\tpip install -e src/ColBERT\n   209\tpip install scipy scikit-learn\n   210\tpip install setuptools==56.1.0\n   211\t```\n   212\t\n   213\tNote: All experiments were run on 8 V100/A100 clusters. You may want to reduce batch sizes or use smaller base models (e.g. TaPEx-base) to fit the memory if your GPUs are smaller.\n   214\t\n   215\t# Data\n   216\tWe use open-sourced data which can be found in the respective papers, please check the references in our papers. Make sure to add/change the data paths in the respective config files. \n   217\t\n   218\t# Useful Command-line Arguments\n   219\tSome general cli arguments. For more details, please read the code / directly look at how they are used in training/evaluation of specific models.\n   220\t\n   221\t## Universal\n   222\t- All trainer parameters supported by pytorch-lightning, such as `--accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2`\n   223\t- `--experiment_name EXPERIMENT_NAME` the name of the experiment. Will be used as the name of the folder as well as the run name on WANDB\n   224\t- `--mode [train/test]` indicate the mode for running. \n   225\t- `--modules module1 module2 module3 ...` list of modules that will be used. They will be saved to `self.config.model_config.modules` so that they are accessible anywhere in the framework.\n   226\t\n   227\t\n   228\t## Training\n   229\t\n   230\t- `--opts [list of configurations]` used at the end of the cli command. `self.config` will be overwritten by the configurations here. For example:\n   231\t\n   232\t  - `train.batch_size=1` batch size\n   233\t  - `train.scheduler=linear` currently supports none/linear\n   234\t  - `train.epochs=20`\n   235\t  - `train.lr=0.00002`\n   236\t  - `train.retriever_lr=0.00001`\n   237\t  - `train.additional.gradient_accumulation_steps=4` \n   238\t  - `train.additional.warmup_steps=0`\n   239\t  - `train.additional.early_stop_patience=7` \n   240\t  - `train.additional.save_top_k=1`\n   241\t  - `valid.step_size=400`\n   242\t  - `valid.batch_size=4`\n   243\t  - `model_config.GeneratorModelVersion=microsoft/tapex-large`: an example of how you can change the pretrained model checkpoint for the answer generator\n   244\t  - `data_loader.additional.num_knowledge_passages=5`: an example of how you can change `K` in ITR/ITR+TableQA/OpenDomainRA-TableQA\n   245\t  - `model_config.num_beams=5`: number of beams in generation\n   246\t\n   247\t## Testing\n   248\t\n   249\t- `--test_evaluation_name nq_tables_all` this will create a folder under the experiment folder (indicated by `--experiment_name`) and save everything there. Also, in the WANDB run (run name indicated by `--experiment_name`), a new section with this name (`nq_tables_all`) will be created, and the evaluation scores will be logged into this section.\n   250\t- `--opts test.batch_size=32` \n   251\t- `--opts test.load_epoch=3825` which checkpoint to load. Note that you need to have the same experiment name\n   252\t\n   253\t\n   254\t\n   255\t# Scripts for Inner Table Retriever\n   256\t\n   257\t## Inner Table Retrieval\n   258\t**NOTE** After training, you need to run inference to generate index files that are used in TableQA + ITR. The index files will be generated in `Experiments/{experiment_name}/test/epoch{load_epoch}/`.\n   259\t\n   260\t## Main Experiments \n   261\t\n   262\t### ITR mix (intersecting columns and rows)\n   263\t\n   264\tWikiSQL train\n   265\t```\n   266\tpython src/main.py configs/wikisql/dpr_ITR_mix_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_mixed --mode train --override --opts train.batch_size=1 train.scheduler=None train.epochs=20 train.lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=200 train.additional.early_stop_patience=8 train.additional.save_top_k=3 valid.batch_size=8 test.batch_size=8 valid.step_size=200 reset=1\n   267\t```\n   268\tWikiSQL test\n   269\t```\n   270\tpython src/main.py configs/wikisql/dpr_ITR_mix_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_mixed --mode test --test_evaluation_name original_sets --opts test.batch_size=32 test.load_epoch=11604\n   271\t```\n   272\tWikiTQ test\n   273\t```\n   274\tpython src/main.py configs/wtq/dpr_ITR_mix_wtq.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_mixed --mode test --test_evaluation_name wtq_original_sets --opts test.batch_size=32 test.load_epoch=11604\n   275\t```\n   276\t\n   277\t## Additional ablation experiments \n   278\t\n   279\t### Column-wise ITR\n   280\tWikiSQL train\n   281\t```\n   282\tpython src/main.py configs/dpr_ITR_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_single_column --mode train --opts train.batch_size=1 train.scheduler=None train.epochs=20 train.lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=200 train.additional.early_stop_patience=6 train.additional.save_top_k=3 train.save_interval=200 valid.batch_size=8 test.batch_size=8 valid.step_size=200 data_loader.dummy_dataloader=0\n   283\t```\n   284\tWikiSQL test\n   285\t```\n   286\tpython src/main.py configs/dpr_ITR_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_single_column --mode test --test_evaluation_name wikisql_original_sets --opts test.batch_size=32 test.load_epoch=3801\n   287\t```\n   288\tWikiTQ test\n   289\t```\n   290\tpython src/main.py configs/wtq/dpr_ITR_column_wise_wtq.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_single_column --mode test --test_evaluation_name wtq_original_sets --opts test.batch_size=32 test.load_epoch=3801\n   291\t```\n   292\t\n   293\t### Row-wise ITR\n   294\tWikiSQL train\n   295\t```\n   296\tpython src/main.py configs/dpr_ITR_row_wise_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_single_row --mode train --opts train.batch_size=1 train.scheduler=None train.epochs=20 train.lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=200 train.additional.early_stop_patience=8 train.additional.save_top_k=3 train.save_interval=200 valid.batch_size=8 test.batch_size=8 valid.step_size=200 data_loader.dummy_dataloader=0\n   297\t```\n   298\tWikiSQL test\n   299\t```\n   300\tpython src/main.py configs/dpr_ITR_row_wise_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_single_row --mode test --test_evaluation_name wikisql_original_sets --opts test.batch_size=32 test.load_epoch=6803\n   301\t```\n   302\tWikiTQ test\n   303\t```\n   304\tpython src/main.py configs/wtq/dpr_ITR_row_wise_wikisql.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_single_row --mode test --test_evaluation_name wtq_original_sets --opts test.batch_size=32 test.load_epoch=6803\n   305\t```\n   306\t\n   307\t\n   308\t## TableQA with ITR (ITR + TaPEx)\n   309\tYou will need to change the index path in the config files. You can also change the index path by `--opts` dynamically.\n   310\t\n   311\tFor example, after running the inference for ITR-mix, you can change the config file `configs/wikisql/tapas_ITR_mix_wikisql.jsonnet` as follows:\n   312\t\n   313\t```\n   314\t// here we put the index file paths\n   315\tlocal index_files = {\n   316\t  \&quot;index_paths\&quot;: {\n   317\t    \&quot;train\&quot;: \&quot;DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_mixed/test/original_sets/step_11604/test.ITRWikiSQLDataset.train\&quot;,\n   318\t    \&quot;validation\&quot;: \&quot;DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_mixed/test/original_sets/step_11604/test.ITRWikiSQLDataset.validation\&quot;,\n   319\t    \&quot;test\&quot;: \&quot;DPR_InnerTableRetrieval_wikisql_with_in_batch_neg_sampling_mixed/test/original_sets/step_11604/test.ITRWikiSQLDataset.test\&quot;,\n   320\t  },\n   321\t};\n   322\t```\n   323\t\n   324\tSome general notes:\n   325\t- You can set `test.load_epoch=0 model_config.GeneratorModelVersion=microsoft/tapex-large-finetuned-wikisql` to use the official checkpoint in evaluation.\n   326\t- Do make sure `test.load_epoch=` has the same number as the checkpoint. Otherwise, `model_config.GeneratorModelVersion` will be loaded.\n   327\t- Check arguments carefully before running! Especially for dangerous commands such as `--override --opts reset=1`.\n   328\t- `data_loader.additional.max_decoder_source_length=128` controls the token limit for ITR, they are 1024 for TaPEx and 512 for TaPas by default.\n   329\t- In the paper we provide different variations. Here we only show the main results with the ITR-mix strategy and the ablations with column- or row-wise ITR. Feel free to try your own configuration. You can change `model_config.ModelClass` to `ITRRagReduceMixModel`, `ITRRagAdditionRowWiseModel`, etc. Available model classes are in `models/itr_rag.py` and `models/itr_rag_reduce.py`.\n   330\t\n   331\t\n   332\t## Main Experiments \n   333\t\n   334\t### ITR mix (intersecting columns and rows)\n   335\t\n   336\tWikiSQL train\n   337\t```\n   338\tpython src/main.py configs/wikisql/tapex_ITR_mix_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name finetune_tapex_large_on_WikiSQL_with_ITR_mix_reduction_smoothing_overflow_only_original_sub_table_order --mode train --modules overflow_only original_sub_table_order shuffle_sub_table_order_in_training --override --opts train.batch_size=1 train.scheduler=linear train.epochs=10 train.lr=0.00003 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=1000 train.additional.early_stop_patience=7 train.additional.save_top_k=3 valid.step_size=1000 valid.batch_size=2 test.batch_size=2 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large data_loader.additional.num_knowledge_passages=10 reset=1\n   339\t```\n   340\tWikiSQL test\n   341\t```\n   342\tpython src/main.py configs/wikisql/tapex_ITR_mix_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name finetune_tapex_large_on_WikiSQL_with_ITR_mix_reduction_smoothing_overflow_only_original_sub_table_order --mode test --modules overflow_only original_sub_table_order --test_evaluation_name original_sets --opts test.batch_size=4 test.load_epoch=[] model_config.GeneratorModelVersion=microsoft/tapex-large model_config.ModelClass=ITRRagReduceMixModel data_loader.additional.num_knowledge_passages=1 data_loader.additional.max_decoder_source_length=1024\n   343\t```\n   344\tWikiTQ train\n   345\t```\n   346\tpython src/main.py configs/wtq/tapex_ITR_mix_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name finetune_tapex_large_on_WTQ_with_ITR_mix_reduction_smoothing_overflow_only_original_sub_table_order_K_10 --mode train --modules overflow_only original_sub_table_order --override --opts train.batch_size=1 train.scheduler=linear train.epochs=40 train.lr=0.00002 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=7 train.additional.save_top_k=3 valid.step_size=1000 valid.batch_size=2 test.batch_size=2 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large model_config.ModelClass=ITRRagReduceMixModel data_loader.additional.num_knowledge_passages=10\n   347\t```\n   348\tWikiTQ test\n   349\t```\n   350\tpython src/main.py configs/wtq/tapex_ITR_mix_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name finetune_tapex_large_on_WTQ_with_ITR_mix_reduction_smoothing_overflow_only_original_sub_table_order_K_10 --mode test --modules overflow_only original_sub_table_order --test_evaluation_name original_sets --opts test.batch_size=2 test.load_epoch=[] model_config.GeneratorModelVersion=microsoft/tapex-large-finetuned-wtq model_config.ModelClass=ITRRagReduceMixModel data_loader.additional.num_knowledge_passages=10 data_loader.additional.max_decoder_source_length=1024\n   351\t```\n   352\t\n   353\t## Additional Ablation Experiments \n   354\t\n   355\t### Column-wise ITR\n   356\tWikiSQL train\n   357\t```\n   358\tpython src/main.py configs/tapex_ITR_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name finetune_tapex_large_on_WikiSQL_with_ITR_addition_smoothing_overflow_only --mode train --modules overflow_only --opts train.batch_size=1 train.scheduler=linear train.epochs=10 train.lr=0.00003 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=1000 train.additional.early_stop_patience=5 train.additional.save_top_k=3 valid.step_size=1000 valid.batch_size=4 test.batch_size=4 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large data_loader.additional.num_knowledge_passages=5\n   359\t```\n   360\tWikiSQL test\n   361\t```\n   362\tpython src/main.py configs/wikisql/tapex_ITR_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name finetune_tapex_large_on_WikiSQL_with_ITR_addition_smoothing_overflow_only_original_sub_table_order --modules overflow_only original_sub_table_order --mode test --test_evaluation_name ITR_addition_oo_osto_official --opts test.batch_size=4 test.load_epoch=[] model_config.GeneratorModelVersion=microsoft/tapex-large  model_config.ModelClass=ITRRagModel data_loader.additional.num_knowledge_passages=5\n   363\t```\n   364\tWikiTQ train\n   365\t```\n   366\tpython src/main.py configs/wtq/tapex_ITR_column_wise_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name finetune_tapex_large_on_WTQ_with_ITR_column_wise_addition_smoothing_overflow_only_original_sub_table_order_K_10 --override --mode train --modules overflow_only original_sub_table_order --opts train.batch_size=1 train.scheduler=linear train.epochs=40 train.lr=0.00002 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=7 train.additional.save_top_k=3 valid.step_size=1000 valid.batch_size=2 test.batch_size=2 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large model_config.ModelClass=ITRRagModel data_loader.additional.num_knowledge_passages=10 reset=1\n   367\t```\n   368\tWikiTQ test\n   369\t```\n   370\tpython src/main.py configs/wtq/tapex_ITR_column_wise_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name finetune_tapex_large_on_WTQ_with_ITR_column_wise_addition_smoothing_overflow_only_original_sub_table_order_K_10 --mode test --modules overflow_only original_sub_table_order --test_evaluation_name ITR_addition_oo_osto_K_10 --opts test.batch_size=2 test.load_epoch=[] model_config.GeneratorModelVersion=microsoft/tapex-large model_config.ModelClass=ITRRagModel data_loader.additional.num_knowledge_passages=10\n   371\t```\n   372\t\n   373\t### Row-wise ITR\n   374\t\n   375\tWikiSQL train\n   376\t```\n   377\tpython src/main.py configs/tapex_ITR_row_wise_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name finetune_tapex_large_on_WikiSQL_with_ITR_row_wise_addition_smoothing_overflow_only_original_sub_table_order_K_10 --mode train --modules overflow_only original_sub_table_order force_select_last --opts train.batch_size=1 train.scheduler=linear train.epochs=10 train.lr=0.00003 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=1000 train.additional.early_stop_patience=7 train.additional.save_top_k=3 valid.step_size=1000 valid.batch_size=2 test.batch_size=2 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large model_config.ModelClass=ITRRagAdditionRowWiseModel data_loader.additional.num_knowledge_passages=10\n   378\t```\n   379\tWikiSQL test\n   380\t```\n   381\tpython src/main.py configs/tapex_ITR_row_wise_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name finetune_tapex_large_on_WikiSQL_with_ITR_row_wise_addition_smoothing_overflow_only_original_sub_table_order_K_10 --mode test --modules overflow_only original_sub_table_order --test_evaluation_name ITR_reduction_oo_osto_K_10 --opts test.batch_size=2 test.load_epoch=[] model_config.GeneratorModelVersion=microsoft/tapex-large model_config.ModelClass=ITRRagReduceRowWiseModel data_loader.additional.num_knowledge_passages=10\n   382\t```\n   383\tWikiTQ train\n   384\t```\n   385\tpython src/main.py configs/wtq/tapex_ITR_row_wise_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name finetune_tapex_large_on_WTQ_with_ITR_row_wise_addition_smoothing_overflow_only_original_sub_table_order_K_10 --mode train --modules overflow_only original_sub_table_order force_select_last --opts train.batch_size=1 train.scheduler=linear train.epochs=30 train.lr=0.00003 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=1000 train.additional.early_stop_patience=7 train.additional.save_top_k=3 valid.step_size=1000 valid.batch_size=2 test.batch_size=2 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large model_config.ModelClass=ITRRagAdditionRowWiseModel data_loader.additional.num_knowledge_passages=10\n   386\t```\n   387\tWikiTQ test\n   388\t```\n   389\tpython src/main.py configs/wtq/tapex_ITR_row_wise_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name finetune_tapex_large_on_WTQ_with_ITR_row_wise_addition_smoothing_overflow_only_original_sub_table_order_K_10 --mode test --modules overflow_only original_sub_table_order force_select_last --test_evaluation_name ITR_addition_oo_osto_fsl_K_10 --opts test.batch_size=4 test.load_epoch=[] model_config.GeneratorModelVersion=microsoft/tapex-large model_config.ModelClass=ITRRagAdditionRowWiseModel data_loader.additional.num_knowledge_passages=10\n   390\t```\n   391\t\n   392\t\n   393\t## TableQA with ITR (TaPas + ITR)\n   394\tWikiSQL test\n   395\t```\n   396\tpython src/main.py configs/wikisql/tapas_ITR_mix_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name evaluate_tapas_with_ITR_on_WikiSQL --mode test --modules overflow_only original_sub_table_order --test_evaluation_name 128tokens --opts test.batch_size=32 test.load_epoch=0 model_config.GeneratorModelVersion=google/tapas-large-finetuned-wikisql-supervised model_config.ModelClass=ITRRagReduceMixModel data_loader.additional.num_knowledge_passages=1 data_loader.additional.max_decoder_source_length=128 model_config.min_columns=1\n   397\t```\n   398\t\n   399\tWikiTQ test\n   400\t```\n   401\tpython src/main.py configs/wtq/tapas_ITR_mix_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name evaluate_tapas_with_ITR_on_WTQ_token_limit_exploration --mode test --modules overflow_only original_sub_table_order --test_evaluation_name 128tokens_official --opts test.batch_size=16 test.load_epoch=0 model_config.GeneratorModelVersion=google/tapas-large-finetuned-wtq model_config.ModelClass=ITRRagReduceMixModel data_loader.additional.num_knowledge_passages=1 data_loader.additional.max_decoder_source_length=128 model_config.min_columns=1\n   402\t```\n   403\t\n   404\t## TableQA with ITR (OmniTab + ITR)\n   405\t```\n   406\tpython src/main.py configs/wtq/tapex_ITR_mix_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name evaluate_ominitab_on_WTQ --mode test --modules overflow_only original_sub_table_order --test_evaluation_name original_sets --opts test.batch_size=2 test.load_epoch=0 model_config.GeneratorModelVersion=neulab/omnitab-large-finetuned-wtq model_config.DecoderTokenizerModelVersion=neulab/omnitab-large-finetuned-wtq model_config.ModelClass=ITRRagReduceMixModel data_loader.additional.num_knowledge_passages=10\n   407\t```\n   408\t\n   409\t## Disable ITR\n   410\t Add `--modules suppress_ITR`, for example:\n   411\t```\n   412\tpython src/main.py configs/wtq/tapas_ITR_mix_wtq.jsonnet --accelerator gpu --devices 8 --strategy ddp --experiment_name evaluate_tapas_with_ITR_on_WTQ_token_limit_exploration --mode test --modules overflow_only original_sub_table_order suppress_ITR --test_evaluation_name 128tokens_official_no_ITR --opts test.batch_size=16 test.load_epoch=0 model_config.GeneratorModelVersion=google/tapas-large-finetuned-wtq model_config.ModelClass=ITRRagReduceMixModel data_loader.additional.num_knowledge_passages=1 data_loader.additional.max_decoder_source_length=128 model_config.min_columns=1\n   413\t```\n   414\t\n   415\t## TableQA Baselines\n   416\t\n   417\t### TaPEx\n   418\tWikiSQL train\n   419\t```\n   420\tpython src/main.py configs/wikisql/tapex_wikisql.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name finetune_tapex_large_on_WikiSQL_smoothing_0.1 --mode train --opts train.batch_size=1 train.scheduler=linear train.epochs=20 train.lr=0.00003 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=1000 train.additional.early_stop_patience=6 train.additional.save_top_k=3 train.save_interval=1000 valid.batch_size=4 test.batch_size=4 data_loader.dummy_dataloader=0 train.additional.label_smoothing_factor=0.1\n   421\t```\n   422\tWikiSQL test\n   423\t```\n   424\tpython src/main.py configs/wikisql/tapex_wikisql.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name finetune_tapex_large_on_WikiSQL_smoothing_0.1 --mode test --log_prediction_tables --test_evaluation_name original_valid_test_set --opts test.batch_size=16 test.load_epoch=[]\n   425\t```\n   426\tChange the config to `configs/wtq/tapex_wtq.jsonnet` to use WTQ instead.\n   427\t\n   428\t\n   429\t# Scripts for Open-domain TableQA with Late Interaction Models\n   430\t\n   431\t## Main Experiments \n   432\t\n   433\t### ColBERT Retrieval\n   434\tSome additional useful arguments:\n   435\t- `model_config.nbits=2`: how many bits the embeddings are quantized (compressed) into. A higher nbits will significantly increase the index size.\n   436\t\n   437\tNQ-TABLES train\n   438\t```\n   439\tpython src/main.py configs/nq_tables/colbert.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name ColBERT_NQTables_bz4_negative4_fix_doclen_full_search_NewcrossGPU --mode train --override --opts train.batch_size=6 train.scheduler=None train.epochs=1000 train.lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=10 train.additional.save_top_k=3 valid.batch_size=32 test.batch_size=32 valid.step_size=200 data_loader.dummy_dataloader=0 reset=1 model_config.num_negative_samples=4 model_config.bm25_top_k=5 model_config.bm25_ratio=0 model_config.nbits=2\n   440\t```\n   441\t\n   442\tNQ-TABLES test\n   443\t```\n   444\tpython src/main.py configs/nq_tables/colbert.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name ColBERT_NQTables_bz4_negative4_fix_doclen_full_search_NewcrossGPU --mode test --test_evaluation_name nq_tables_all --opts test.batch_size=32 test.load_epoch=5427 model_config.nbits=8\n   445\t```\n   446\t\n   447\tE2E_WTQ train\n   448\t```\n   449\tpython src/main.py configs/e2e_wtq/colbert.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 0 --experiment_name ColBERT_E2EWTQ_bz4_negative4_fix_doclen_full_search_NewcrossGPU --mode train --override --modules exhaustive_search_in_testing --opts train.batch_size=6 train.scheduler=None train.epochs=1000 train.lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=30 train.additional.save_top_k=3 valid.batch_size=32 test.batch_size=32 valid.step_size=10 data_loader.dummy_dataloader=0 reset=1 model_config.num_negative_samples=4 model_config.bm25_top_k=5 model_config.bm25_ratio=0 model_config.nbits=2\n   450\t```\n   451\t\n   452\tE2E_WTQ test\n   453\t```\n   454\tpython src/main.py configs/e2e_wtq/colbert.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name ColBERT_E2EWTQ_bz4_negative4_fix_doclen_full_search_NewcrossGPU --mode test --test_evaluation_name e2e_wtq_all --opts test.batch_size=32 test.load_epoch=300 model_config.nbits=8\n   455\t```\n   456\t\n   457\t\n   458\t### LIRAGE\n   459\t**Note**: if you are not using the index files specified in the config files, you may want to \n   460\t- change the paths in the config file; or\n   461\t- use the following to change the paths in commandline directly:\n   462\t```\n   463\t--opts model_config.QueryEncoderModelVersion=$ColBERT_NQTables_bz4_negative4_fix_doclen_full_search_NewcrossGPU/train/saved_model/step_5427 model_config.index_files.index_passages_path=ColBERT_NQTables_bz4_negative4_fix_doclen_full_search_NewcrossGPU/test/nq_tables_all/step_5427/table_dataset model_config.index_files.index_path=ColBERT_NQTables_bz4_negative4_fix_doclen_full_search_NewcrossGPU/test/nq_tables_all/step_5427/table_dataset_colbert_index model_config.index_files.embedding_path=ColBERT_NQTables_bz4_negative4_fix_doclen_full_search_NewcrossGPU/test/nq_tables_all/step_5427/item_embeddings.pkl\n   464\t```\n   465\t\n   466\t\n   467\tNQ-TABLES train\n   468\t```\n   469\tpython src/main.py configs/nq_tables/colbert_rag.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name RAG_ColBERT_NQTables_RAVQA_Approach5_add_prompt --mode train --modules add_binary_labels_as_prompt --override --opts train.batch_size=1 train.scheduler=linear train.epochs=20 train.lr=0.00002 train.retriever_lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=3 train.additional.save_top_k=1 valid.step_size=400 valid.batch_size=4 test.batch_size=4 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large data_loader.additional.num_knowledge_passages=5 model_config.num_beams=5 reset=1\n   470\t```\n   471\tNQ-TABLES test\n   472\t```\n   473\tpython src/main.py configs/nq_tables/colbert_rag.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name RAG_ColBERT_NQTables_RAVQA_Approach5_add_prompt --mode test --test_evaluation_name alternative_answers --modules add_binary_labels_as_prompt --opts test.batch_size=1 test.load_epoch=[] model_config.num_beams=5 data_loader.additional.num_knowledge_passages=5\n   474\t```\n   475\tE2E_WTQ train\n   476\t(using the officially released TaPEx does not differ much from using our own finetuned. To save steps, we just use it here. You can change it to your own finetuned TaPEx version.)\n   477\t```\n   478\tpython src/main.py configs/e2e_wtq/colbert_rag.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name RAG_ColBERT_E2EWTQ_RAVQA_Approach5_add_prompt_pretrained --modules add_binary_labels_as_prompt --mode train --override --opts train.batch_size=1 train.scheduler=none train.epochs=100 train.lr=0.000015 train.retriever_lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=3 train.additional.save_top_k=1 valid.step_size=25 valid.batch_size=4 test.batch_size=4 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large-finetuned-wtq data_loader.additional.num_knowledge_passages=5 model_config.num_beams=5 model_config.RAVQA_loss_type=Approach5 reset=1\n   479\t```\n   480\tE2E_WTQ test\n   481\t```\n   482\tpython src/main.py configs/e2e_wtq/colbert_rag.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name RAG_ColBERT_E2EWTQ_RAVQA_Approach5_add_prompt_pretrained --mode test --test_evaluation_name K5 --modules add_binary_labels_as_prompt --opts test.batch_size=1 test.load_epoch=176 model_config.num_beams=5 data_loader.additional.num_knowledge_passages=5\n   483\t```\n   484\t\n   485\t## Additional Ablation Experiments \n   486\t\n   487\t### Dense Passage Retrieval (DPR)\n   488\t\n   489\tSome useful arguments\n   490\t- `--modules negative_samples_across_gpus`: sharing negative samples across GPUs\n   491\t- `--modules exhaustive_search_in_testing`: use exhaustive search in testing, but this is significantly slower than building HNSW index. HNSW offers faster dynamic search later\n   492\t- `--opts model_config.bm25_ratio=0 model_config.bm25_top_k=3`\n   493\t  - `ratio=0`: no bm25 mined negative examples are used\n   494\t  - `bm25_top_k=K`: find negative examples in top-K bm25 mined examples. Note that this value should be large enough when `model_config.num_negative_samples` is large so that enough examples can be found.\n   495\t  - Note: this didn't improve the performance at the end. So it is disabled now.\n   496\t\n   497\t\n   498\tNQ-TABLES train\n   499\t```\n   500\tpython src/main.py configs/nq_tables/dpr.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name DPR_NQTables_train_bz8_gc_4_crossGPU --mode train --override --modules negative_samples_across_gpus exhaustive_search_in_testing --opts train.batch_size=8 train.scheduler=None train.epochs=1000 train.lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=10 train.additional.save_top_k=3 valid.batch_size=32 test.batch_size=32 valid.step_size=200 data_loader.dummy_dataloader=0 reset=1 model_config.num_negative_samples=4 model_config.bm25_ratio=0 model_config.bm25_top_k=3\n   501\t```\n   502\t\n   503\tNQ-TABLES test\n   504\t```\n   505\tpython src/main.py configs/nq_tables/dpr.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name DPR_NQTables_train_bz8_gc_4_crossGPU --mode test --test_evaluation_name nq_tables_all --opts test.batch_size=32 test.load_epoch=[]\n   506\t```\n   507\t\n   508\tE2E_WTQ train\n   509\t\n   510\t(Note that loading a pre-trained DPR checkpoint does not improve the performance much. If you don't have a checkpoint pre-trained on NQ-TABLES, simply drop `model_config.QueryEncoderModelVersion` and `model_config.ItemEncoderModelVersion`.)\n   511\t\n   512\t```\n   513\tpython src/main.py configs/e2e_wtq/dpr.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name DPR_E2EWTQ_train_bz8_gc_4_neg4 --mode train --override --modules exhaustive_search_in_testing --opts train.batch_size=8 train.scheduler=None train.epochs=300 train.lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=100 train.additional.save_top_k=3 valid.batch_size=32 test.batch_size=32 valid.step_size=10 data_loader.dummy_dataloader=0 reset=1 model_config.num_negative_samples=4 model_config.QueryEncoderModelVersion=/wd/Experiments/DPR_NQTables_train_bz8_gc_4_crossGPU/train/saved_model/step_2039/query_encoder model_config.ItemEncoderModelVersion=/wd/Experiments/DPR_NQTables_train_bz8_gc_4_crossGPU/train/saved_model/step_2039/item_encoder model_config.bm25_top_k=5 model_config.bm25_ratio=0\n   514\t```\n   515\t\n   516\tE2E_WTQ test\n   517\t```\n   518\tpython src/main.py configs/e2e_wtq/dpr.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name DPR_E2EWTQ_train_bz8_gc_4_neg4 --mode test --test_evaluation_name e2e_wtq_all --opts test.batch_size=32 test.load_epoch=480\n   519\t```\n   520\t\n   521\t\n   522\t### DPR + RAGE \n   523\tSome settings:\n   524\t- `--modules add_binary_labels_as_prompt`: add binary relevance tokens in training and in testing. Note: must be enabled both in training and testing.\n   525\t- `nq_tables/rag.jsonnet`: joint training of retriever and reader; `frozen_rag.jsonnet`: freeze the retriever during training.\n   526\t\n   527\tNQ-TABLES train\n   528\t```\n   529\tpython src/main.py configs/nq_tables/rag.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name RAG_NQTables_RAVQA_loss_approach5_add_prompt --mode train --modules add_binary_labels_as_prompt --override --opts train.batch_size=1 train.scheduler=linear train.epochs=20 train.lr=0.00002 train.retriever_lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=3 train.additional.save_top_k=1 valid.step_size=400 valid.batch_size=4 test.batch_size=4 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large data_loader.additional.num_knowledge_passages=5 model_config.num_beams=5 model_config.RAVQA_loss_type=Approach5 reset=1\n   530\t```\n   531\t\n   532\tNQ-TABLES test\n   533\t```\n   534\tpython src/main.py configs/nq_tables/rag.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name RAG_NQTables_RAVQA_loss_approach5_add_prompt --mode test --test_evaluation_name official_sets --modules add_binary_labels_as_prompt --opts test.batch_size=1 test.load_epoch=[] model_config.num_beams=5 data_loader.additional.num_knowledge_passages=5\n   535\t```\n   536\t\n   537\t\n   538\tE2E_WTQ train\n   539\t```\n   540\tpython src/main.py configs/e2e_wtq/rag.jsonnet --accelerator gpu --devices 8 --strategy ddp --num_sanity_val_steps 2 --experiment_name RAG_E2EWTQ_RAVQA_Approach5_add_prompt --mode train --modules add_binary_labels_as_prompt --override --opts train.batch_size=1 train.scheduler=none train.epochs=100 train.lr=0.000015 train.retriever_lr=0.00001 train.additional.gradient_accumulation_steps=4 train.additional.warmup_steps=0 train.additional.early_stop_patience=3 train.additional.save_top_k=-1 valid.step_size=25 valid.batch_size=4 test.batch_size=4 data_loader.dummy_dataloader=0 model_config.GeneratorModelVersion=microsoft/tapex-large-finetuned-wtq data_loader.additional.num_knowledge_passages=5 model_config.num_beams=5 model_config.RAVQA_loss_type=Approach5 reset=1\n   541\t```\n   542\t\n   543\tE2E_WTQ test\n   544\t```\n   545\tpython src/main.py configs/e2e_wtq/rag.jsonnet --accelerator gpu --devices 1 --strategy ddp --experiment_name RAG_E2EWTQ_RAVQA_Approach5_add_prompt --mode test --test_evaluation_name K5 --modules add_binary_labels_as_prompt --opts test.batch_size=1 test.load_epoch=126 model_config.num_beams=5 data_loader.additional.num_knowledge_passages=5\n   546\t```\n   547\t\n   548\t## Miscellaneous  \n   549\t\n   550\t### Difference w.r.t. the official ColBERT repository\n   551\tWe have made several changes to the ColBERT codebase so that it can be run and integrated into our framework.\n   552\t- The original code uses `total_visible_gpus` to determine if we are to use gpus. However, in dynamic retrieval, we'd like to use cpu for retrieval only, while keeping gpus visible to our main training framework. Therefore, we modified `colbert/indexing/codecs/residual.py` to add an argument `disable_gpu`. Similarly, `self.use_gpu` is added to `colbert/search/index_loader.py`. `colbert/searcher.py` reads `initial_config.total_visible_gpus = config.total_visible_gpus` from the pass-in config.\n   553\t- `colbert/indexing/collection_indexer.py`: we commented out `self.config.help()` which generates redundant information flooding the terminal.\n   554\t- `colbert/modeling/colbert.py`: its original negative samples are only shared across batches in one device. We added support for sharing negative samples in all batches across all GPUs. We also re-wrote the original in-batch negative sampling loss with a cleaner version.\n   555\t- `colbert/modeling/tokenization/doc_tokenization.py`: the padding strategy is changed from `longest` to `max_length`. This is because our tables are typically long, and we set `max_length=512`.\n   556\t- Other issues that blocked running\n   557\t  - `colbert/search/index_storage.py`: `approx_scores = torch.cat(approx_scores, dim=0).float()`\n   558\t  - `colbert/search/strided_tensor.py`: `pids = pids.cpu()` is necessary. Move them to GPU afterwards.\n   559\t\n   560\t\n   561\t## Security\n   562\t\n   563\tSee [CONTRIBUTING](CONTRIBUTING.md#security-issue-notifications) for more information.\n   564\t\n   565\t## License Summary\n   566\t\n   567\tThe documentation is made available under the Creative Commons Attribution-ShareAlike 4.0 International License. See the LICENSE file.\n   568\t\n   569\tThe sample code within this documentation is made available under the MIT-0 license. See the LICENSE-SAMPLECODE file.\n   570\t\nTotal lines in file: 570\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;88cc4cc6-efdf-48f1-8dab-45c9785dfaf5;toolu_01Hic3AC6RneFeMLPdJ2mFdC&quot;:{&quot;requestId&quot;:&quot;88cc4cc6-efdf-48f1-8dab-45c9785dfaf5&quot;,&quot;toolUseId&quot;:&quot;toolu_01Hic3AC6RneFeMLPdJ2mFdC&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in src, excluding hidden items:\nsrc/ColBERT\nsrc/data_loader_manager\nsrc/main.py\nsrc/models\nsrc/trainers\nsrc/utils\nsrc/ColBERT/LICENSE\nsrc/ColBERT/LoTTE.md\nsrc/ColBERT/README.md\nsrc/ColBERT/baleen\nsrc/ColBERT/colbert\nsrc/ColBERT/conda_env.yml\nsrc/ColBERT/conda_env_cpu.yml\nsrc/ColBERT/docs\nsrc/ColBERT/setup.py\nsrc/ColBERT/utility\nsrc/data_loader_manager/__init__.py\nsrc/data_loader_manager/data_loader_for_tableqa.py\nsrc/data_loader_manager/data_loader_wrapper.py\nsrc/data_loader_manager/datasets\nsrc/data_loader_manager/module_parser.py\nsrc/models/dpr\nsrc/models/rag\nsrc/models/tapas\nsrc/trainers/ColBERT_executor.py\nsrc/trainers/DPR_executor.py\nsrc/trainers/ITR_DPR_executor.py\nsrc/trainers/ITR_rag_executor.py\nsrc/trainers/RAG_executor.py\nsrc/trainers/__init__.py\nsrc/trainers/base_executor.py\nsrc/trainers/metrics_processors.py\nsrc/trainers/tapas_executor.py\nsrc/trainers/tapex_executor.py\nsrc/utils/cache_system.py\nsrc/utils/collect_env.py\nsrc/utils/color_logging.py\nsrc/utils/config_system.py\nsrc/utils/cuda_stats.py\nsrc/utils/dirs.py\nsrc/utils/metrics_log_callback.py\nsrc/utils/seed.py\nsrc/utils/tapas_metrics_utils.py\nsrc/utils/tapas_utils.py\nsrc/utils/text_utils.py\nsrc/utils/wikisql_utils.py\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;44538700-bd34-440d-a3d2-c17a7a4fc7f3;toolu_018WkawMMRXFZro5zQbo3wAx&quot;:{&quot;requestId&quot;:&quot;44538700-bd34-440d-a3d2-c17a7a4fc7f3&quot;,&quot;toolUseId&quot;:&quot;toolu_018WkawMMRXFZro5zQbo3wAx&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in configs, excluding hidden items:\nconfigs/base_env.jsonnet\nconfigs/e2e_wtq\nconfigs/nq_tables\nconfigs/wikisql\nconfigs/wtq\nconfigs/e2e_wtq/colbert.jsonnet\nconfigs/e2e_wtq/colbert_rag.jsonnet\nconfigs/e2e_wtq/dpr.jsonnet\nconfigs/e2e_wtq/frozen_colbert_rag.jsonnet\nconfigs/e2e_wtq/frozen_rag.jsonnet\nconfigs/e2e_wtq/rag.jsonnet\nconfigs/nq_tables/colbert.jsonnet\nconfigs/nq_tables/colbert_rag.jsonnet\nconfigs/nq_tables/dpr.jsonnet\nconfigs/nq_tables/frozen_colbert_rag.jsonnet\nconfigs/nq_tables/frozen_rag.jsonnet\nconfigs/nq_tables/rag.jsonnet\nconfigs/wikisql/dpr_ITR_column_wise_wikisql.jsonnet\nconfigs/wikisql/dpr_ITR_column_wise_wikisql_extreme.jsonnet\nconfigs/wikisql/dpr_ITR_mix_wikisql.jsonnet\nconfigs/wikisql/dpr_ITR_mix_wikisql_extreme.jsonnet\nconfigs/wikisql/dpr_ITR_row_wise_wikisql.jsonnet\nconfigs/wikisql/dpr_ITR_row_wise_wikisql_extreme.jsonnet\nconfigs/wikisql/tapas_ITR_mix_wikisql.jsonnet\nconfigs/wikisql/tapex_ITR_mix_wikisql.jsonnet\nconfigs/wikisql/tapex_ITR_mix_wikisql_extreme.jsonnet\nconfigs/wikisql/tapex_ITR_reduce_wikisql.jsonnet\nconfigs/wikisql/tapex_ITR_row_wise_wikisql.jsonnet\nconfigs/wikisql/tapex_ITR_row_wise_wikisql_extreme.jsonnet\nconfigs/wikisql/tapex_ITR_wikisql.jsonnet\nconfigs/wikisql/tapex_ITR_wikisql_extreme.jsonnet\nconfigs/wikisql/tapex_wikisql.jsonnet\nconfigs/wikisql/tapex_wikisql_extreme.jsonnet\nconfigs/wtq/dpr_ITR_column_wise_wtq.jsonnet\nconfigs/wtq/dpr_ITR_mix_wtq.jsonnet\nconfigs/wtq/dpr_ITR_row_wise_wtq.jsonnet\nconfigs/wtq/tapas_ITR_mix_wtq.jsonnet\nconfigs/wtq/tapas_base.jsonnet\nconfigs/wtq/tapas_use_original_data.jsonnet\nconfigs/wtq/tapex_ITR_column_wise_wtq.jsonnet\nconfigs/wtq/tapex_ITR_mix_wtq.jsonnet\nconfigs/wtq/tapex_ITR_row_wise_wtq.jsonnet\nconfigs/wtq/tapex_base.jsonnet\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}}},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasAgentOnboarded&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;ebc01abd-d5dc-49f1-99cf-61388cab52fd&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>